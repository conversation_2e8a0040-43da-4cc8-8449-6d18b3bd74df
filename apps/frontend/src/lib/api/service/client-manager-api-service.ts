import type { IAddress } from '../../../interfaces/entity/i-address.ts';
import type { IClientEntity } from '../../../interfaces/entity/i-client-entity.ts';
import type { IPaginatedResponse, IPaginationParams } from '../../../interfaces/pagination.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';

export class ClientManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getClients(params?: IPaginationParams): Promise<IPaginatedResponse<IClientEntity>> {
    const response = await this.apiClient.get('/api/manager/clients', { params });
    return response.data;
  }

  async getClientById(id: string): Promise<IClientEntity> {
    const response = await this.apiClient.get(`/api/manager/clients/${id}`);
    return response.data;
  }

  async updateClient(
    id: string,
    data: { email?: string; address?: IAddress },
  ): Promise<IClientEntity> {
    const response = await this.apiClient.patch(`/api/manager/clients/${id}`, data);
    return response.data;
  }
}

export const clientManagerApiService = new ClientManagerApiService(apiClient);
