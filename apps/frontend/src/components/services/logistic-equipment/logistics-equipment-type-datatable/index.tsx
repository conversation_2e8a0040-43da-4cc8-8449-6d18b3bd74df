'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { ILogisticsEquipmentTypeEntity } from '@/interfaces/entity/i-logistics-equipment-type-entity';
import { LogisticsEquipmentKind } from '@/interfaces/enum/logistics-equipment-kind.enum';
import { logisticsEquipmentTypeManagerApiService } from '@/lib/api/service/logistics-equipment-type-manager-api-service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ColumnDef } from '@tanstack/react-table';
import { EditIcon, PlusIcon, TrashIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';
import { CreateLogisticsEquipmentTypeDialog } from './create-logistics-equipment-type-dialog';
import { EditLogisticsEquipmentTypeDialog } from './edit-logistics-equipment-type-dialog';

interface LogisticsEquipmentTypeTableSearchParams extends Record<string, unknown> {
  search?: string;
  kind?: string;
}

export function LogisticsEquipmentTypeDataTable({
  className,
}: LogisticsEquipmentTypeDataTableProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingEquipmentType, setEditingEquipmentType] =
    useState<ILogisticsEquipmentTypeEntity | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const queryClient = useQueryClient();

  const deleteMutation = useMutation({
    mutationFn: (id: string) => logisticsEquipmentTypeManagerApiService.delete(id),
    onSuccess: () => {
      toast.success("Type d'équipement supprimé avec succès");
      queryClient.invalidateQueries({ queryKey: ['logistics-equipment-types'] });
    },
    onError: (error: unknown) => {
      const message =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data
          ? String(error.response.data.message)
          : 'Une erreur est survenue lors de la suppression';
      toast.error(message);
    },
  });

  const handleEditEquipmentType = (equipmentType: ILogisticsEquipmentTypeEntity) => {
    setEditingEquipmentType(equipmentType);
    setIsEditDialogOpen(true);
  };

  const handleEditEquipmentTypes = (selectedEquipmentTypes: ILogisticsEquipmentTypeEntity[]) => {
    if (selectedEquipmentTypes.length === 1) {
      handleEditEquipmentType(selectedEquipmentTypes[0]);
    }
  };

  const handleDeleteEquipmentTypes = async (
    selectedEquipmentTypes: ILogisticsEquipmentTypeEntity[],
  ) => {
    if (
      confirm(
        `Êtes-vous sûr de vouloir supprimer ${selectedEquipmentTypes.length} type(s) d'équipement ?`,
      )
    ) {
      for (const equipmentType of selectedEquipmentTypes) {
        await deleteMutation.mutateAsync(equipmentType.id);
      }
    }
  };

  const handleAddEquipmentType = () => {
    setIsCreateDialogOpen(true);
  };

  const kindLabels = {
    [LogisticsEquipmentKind.PALLET]: 'Palette',
    [LogisticsEquipmentKind.ROLL]: 'Roll',
    [LogisticsEquipmentKind.PACKAGE]: 'Colis',
  };

  const columns: ColumnDef<ILogisticsEquipmentTypeEntity>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Tout sélectionner"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Sélectionner la ligne"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: 'Nom',
      accessorKey: 'name',
      cell: ({ row }) => <div className="font-medium">{row.original.name}</div>,
      size: 200,
      enableHiding: false,
    },
    {
      header: 'Type',
      accessorKey: 'kind',
      cell: ({ row }) => (
        <Badge variant="outline" className="font-mono">
          {kindLabels[row.original.kind] || row.original.kind}
        </Badge>
      ),
      size: 150,
    },
    {
      header: 'Date de création',
      accessorKey: 'createdAt',
      cell: ({ row }) => {
        const date = new Date(row.original.createdAt);
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString('fr-FR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })}
          </div>
        );
      },
      size: 120,
    },
    {
      header: 'Date de modification',
      accessorKey: 'updatedAt',
      cell: ({ row }) => {
        const date = new Date(row.original.updatedAt);
        return (
          <div className="text-sm text-muted-foreground">
            {date.toLocaleDateString('fr-FR', {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })}
          </div>
        );
      },
      size: 120,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditEquipmentType(row.original)}
            className="h-8 w-8 p-0"
          >
            <EditIcon className="h-4 w-4" />
            <span className="sr-only">Modifier {row.original.name}</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => deleteMutation.mutate(row.original.id)}
            className="h-8 w-8 p-0"
          >
            <TrashIcon className="h-4 w-4" />
            <span className="sr-only">Supprimer {row.original.name}</span>
          </Button>
        </div>
      ),
      size: 120,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<
    ILogisticsEquipmentTypeEntity,
    LogisticsEquipmentTypeTableSearchParams
  > = {
    export: {
      enabled: true,
      fileName: 'types-equipement-logistique',
      sheetName: "Types d'équipement",
      excludeColumns: ['select', 'actions'],
      columnMap: {
        name: 'Nom',
        kind: 'Type',
        createdAt: 'Date de création',
        updatedAt: 'Date de modification',
      },
      valueTransformers: {
        kind: (value) => kindLabels[value as LogisticsEquipmentKind] || String(value),
        createdAt: (value) => {
          return new Date(value as string).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          });
        },
        updatedAt: (value) => {
          return new Date(value as string).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          });
        },
      },
    },
    columns,
    search: {
      enabled: true,
      placeholder: "Rechercher des types d'équipement par nom...",
      searchKey: 'search',
    },
    filters: [
      {
        columnKey: 'kind',
        label: 'Type',
        type: 'select',
        options: [
          { label: 'Tous les types', value: 'all' },
          { label: 'Palette', value: LogisticsEquipmentKind.PALLET },
          { label: 'Roll', value: LogisticsEquipmentKind.ROLL },
          { label: 'Colis', value: LogisticsEquipmentKind.PACKAGE },
        ],
        placeholder: 'Filtrer par type',
      },
    ],
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Modifier',
        icon: <EditIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleEditEquipmentTypes,
        variant: 'outline',
        requiresSelection: true,
      },
      {
        label: 'Supprimer',
        icon: <TrashIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleDeleteEquipmentTypes,
        variant: 'destructive',
        requiresSelection: true,
      },
      {
        label: 'Ajouter un type',
        icon: <PlusIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleAddEquipmentType,
        variant: 'outline',
        requiresSelection: false,
      },
    ],
  };

  return (
    <>
      <PaginatedDataTable
        queryKey={['logistics-equipment-types', 'paginated']}
        queryFn={(params) => logisticsEquipmentTypeManagerApiService.findAll(params)}
        config={config}
        className={className}
      />

      <CreateLogisticsEquipmentTypeDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />
      <EditLogisticsEquipmentTypeDialog
        equipmentType={editingEquipmentType}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      />
    </>
  );
}

interface LogisticsEquipmentTypeDataTableProps {
  className?: string;
}

export default LogisticsEquipmentTypeDataTable;
