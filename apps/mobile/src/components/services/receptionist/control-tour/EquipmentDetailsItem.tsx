import { MinusIcon, PlusIcon } from 'lucide-react';
import { ILogisticsEquipmentTypeEntity } from '../../../../interfaces/entity/i-logistics-equipment-type-entity';
import { receptionistReturnControlActions } from '../../../../stores/receptionistReturnControlSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';

interface Props {
  equipmentType: ILogisticsEquipmentTypeEntity;
}

export function EquipmentDetailsItem({ equipmentType }: Props) {
  const equipmentValidationQuantity = useAppSelector(
    (state) => state.receptionistReturnControl.equipmentValidationDetails[equipmentType.id] || 0,
  );
  const dispatch = useAppDispatch();

  const handleValueChange = (value: number) => {
    if (value < 0) {
      return;
    }

    dispatch(
      receptionistReturnControlActions.setEquipmentValidationQuantity({
        equipmentTypeId: equipmentType.id,
        quantity: value,
      }),
    );
  };

  return (
    <div className="flex items-center justify-between">
      <div>{equipmentType.name}</div>
      <div>
        <div className="flex items-center gap-2">
          <button onClick={() => handleValueChange(equipmentValidationQuantity - 1)}>
            <MinusIcon />
          </button>
          <input
            type="number"
            value={equipmentValidationQuantity}
            onChange={(e) => handleValueChange(Number(e.target.value))}
          />
          <button onClick={() => handleValueChange(equipmentValidationQuantity + 1)}>
            <PlusIcon />
          </button>
        </div>
      </div>
    </div>
  );
}
