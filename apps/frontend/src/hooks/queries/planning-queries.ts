import type { ITourAssignmentEntity } from '@/interfaces/entity/i-tour-assignment-entity.ts';
import type { ITourEntity } from '@/interfaces/entity/i-tour-entity.ts';
import type { ITourIdentifier } from '@/interfaces/entity/i-tour-identifier.ts';
import { planningApiService } from '@/lib/api/service/planning-api-service.ts';
import { keycloakService } from '@/lib/services/keycloak.service.ts';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';

export const planningQueryKeys = {
  all: ['planning'] as const,
  tourIdentifiers: (fromDate: Date, toDate: Date) =>
    [
      ...planningQueryKeys.all,
      'tour-identifiers',
      format(fromDate, 'yyyy-MM-dd'),
      format(toDate, 'yyyy-MM-dd'),
    ] as const,
  tours: (fromDate: Date, toDate: Date) =>
    [
      ...planningQueryKeys.all,
      'tours',
      format(fromDate, 'yyyy-MM-dd'),
      format(toDate, 'yyyy-MM-dd'),
    ] as const,
  assignments: (fromDate: Date, toDate: Date) =>
    [
      ...planningQueryKeys.all,
      'assignments',
      format(fromDate, 'yyyy-MM-dd'),
      format(toDate, 'yyyy-MM-dd'),
    ] as const,
};

export const useTourIdentifiersQuery = (fromDate: Date, toDate: Date) => {
  return useQuery({
    queryKey: planningQueryKeys.tourIdentifiers(fromDate, toDate),
    queryFn: async (): Promise<ITourIdentifier[]> => {
      if (!keycloakService.keycloak.authenticated) {
        return [];
      }
      return planningApiService.getDistinctTourIdentifiers(fromDate, toDate);
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
};

export const useToursQuery = (fromDate: Date, toDate: Date) => {
  return useQuery({
    queryKey: planningQueryKeys.tours(fromDate, toDate),
    queryFn: async (): Promise<ITourEntity[]> => {
      if (!keycloakService.keycloak.authenticated) {
        return [];
      }
      const response = await planningApiService.getToursByDateRange(fromDate, toDate, {
        limit: 1000,
      });
      return response.items;
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
};

export const useTourAssignmentsQuery = (fromDate: Date, toDate: Date) => {
  return useQuery({
    queryKey: planningQueryKeys.assignments(fromDate, toDate),
    queryFn: async (): Promise<ITourAssignmentEntity[]> => {
      if (!keycloakService.keycloak.authenticated) {
        return [];
      }

      // Helper to fetch all assignments with pagination
      let allAssignments: ITourAssignmentEntity[] = [];
      let page = 1;
      const limit = 100;
      let hasMoreData = true;

      while (hasMoreData) {
        const response = await planningApiService.getTourAssignmentsByDateRange(fromDate, toDate, {
          page,
          limit,
        });
        allAssignments = [...allAssignments, ...response.items];
        hasMoreData = response.meta.totalPages > page;
        page++;
      }

      return allAssignments;
    },
    enabled: keycloakService.keycloak.authenticated,
  });
};
