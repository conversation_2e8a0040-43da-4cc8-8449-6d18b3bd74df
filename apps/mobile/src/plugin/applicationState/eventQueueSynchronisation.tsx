import React, { useEffect } from 'react';
import {
  deleteOldEvents,
  initializeEventQueueState,
  processEvent,
} from '../../stores/eventQueueSlice';
import { useAppDispatch, useAppSelector } from '../../utils/redux';

export const useEventQueueSynchronisation = () => {
  const dispatch = useAppDispatch();
  const queue = useAppSelector((state) => state.eventQueue.queue);
  const isInitialized = useAppSelector((state) => state.eventQueue.isInitialized);
  const hasInternet = useAppSelector((state) => state.applicationState.hasInternet);

  useEffect(() => {
    dispatch(initializeEventQueueState());
  }, [dispatch]);

  useEffect(() => {
    setTimeout(() => {
      dispatch(deleteOldEvents());
    }, 60000);
  }, [dispatch]);

  useEffect(() => {
    console.log('🔍 [EventQueueSync] Queue state changed:', {
      isInitialized,
      hasInternet,
      queueLength: queue.length,
      queue: queue.map((q) => ({
        id: q.id,
        eventType: q.eventType,
        status: q.completionStatus.status,
        createdAt: q.createdAt,
      })),
    });

    if (!isInitialized) {
      console.log('🔍 [EventQueueSync] Not processing queue - not initialized');

      return;
    }

    if (!hasInternet) {
      console.log('🔍 [EventQueueSync] Not processing queue - no internet');

      return;
    }

    if (queue.length === 0) {
      console.log('🔍 [EventQueueSync] Not processing queue - empty queue');

      return;
    }

    const hasSyncingEvent = queue.some((element) => element.completionStatus.status === 'syncing');

    if (hasSyncingEvent) {
      console.log('🔍 [EventQueueSync] Not processing queue - event already syncing');

      return;
    }

    // Get all pending events and sort by creation time (FIFO)
    const pendingEvents = queue
      .filter((element) => element.completionStatus.status === 'pending')
      .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());

    console.log('🔍 [EventQueueSync] Pending events:', {
      pendingCount: pendingEvents.length,
      pendingEvents: pendingEvents.map((e) => ({
        id: e.id,
        eventType: e.eventType,
      })),
    });

    const nextElement = pendingEvents[0];

    if (nextElement) {
      console.log('🔍 [EventQueueSync] Processing next event:', {
        id: nextElement.id,
        eventType: nextElement.eventType,
      });
      dispatch(processEvent(nextElement));
    } else {
      console.log('🔍 [EventQueueSync] No pending events to process');
    }
  }, [queue, dispatch, isInitialized, hasInternet]);
};

export const EventQueueSynchronization: React.FC = () => {
  useEventQueueSynchronisation();

  return null;
};
