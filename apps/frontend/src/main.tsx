import { QueryClientProvider } from '@tanstack/react-query';
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router';
import App from './app';
import './assets/styles/index.css';
import { queryClient } from '@/lib/api/client/tanstack-query';
import { keycloakService } from './lib/services/keycloak.service';
import { store } from './lib/store/store';

keycloakService
  .init()
  .then(() => {
    createRoot(document.getElementById('root')!).render(
      <StrictMode>
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <BrowserRouter>
              <App />
            </BrowserRouter>
          </QueryClientProvider>
        </Provider>
      </StrictMode>,
    );
  })
  .catch((error) => {
    console.error(error);
  });
