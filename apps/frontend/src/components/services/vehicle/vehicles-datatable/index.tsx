'use client';

import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { IVehicleEntity } from '@/interfaces/entity/i-vehicle-entity';
import { vehicleManagerApiService } from '@/lib/api/service/vehicle-manager-api-service';
import { ColumnDef } from '@tanstack/react-table';
import { EditIcon, PlusIcon, TrashIcon } from 'lucide-react';
import { useState } from 'react';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';
import { VehicleForm } from '../vehicle-form';

interface VehicleTableSearchParams extends Record<string, unknown> {
  search?: string;
}

const columns: ColumnDef<IVehicleEntity>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Tout sélectionner"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Sélectionner la ligne"
      />
    ),
    size: 28,
    enableSorting: false,
    enableHiding: false,
  },
  {
    header: 'Immatriculation',
    accessorKey: 'licensePlate',
    cell: ({ row }) => <div className="font-medium">{row.original.licensePlate}</div>,
    size: 200,
    enableHiding: false,
  },
  {
    header: 'Modèle',
    accessorKey: 'model',
    cell: ({ row }) => (
      <div className="max-w-48 truncate">
        {row.original.model || <span className="text-muted-foreground italic">Aucun modèle</span>}
      </div>
    ),
    size: 200,
  },
  {
    header: "Numéro d'assurance",
    accessorKey: 'insuranceNumber',
    cell: ({ row }) => (
      <div className="max-w-48 truncate">
        {row.original.insuranceNumber || (
          <span className="text-muted-foreground italic">Aucun numéro</span>
        )}
      </div>
    ),
    size: 200,
  },
  {
    header: "Date d'expiration contrôle technique",
    accessorKey: 'technicalControlExpiryDate',
    cell: ({ row }) => {
      const date = row.original.technicalControlExpiryDate;
      if (!date) {
        return <span className="text-muted-foreground italic">Non définie</span>;
      }

      const formattedDate = new Date(date).toLocaleDateString('fr-FR');
      const isExpired = new Date(date) < new Date();

      return <Badge variant={isExpired ? 'destructive' : 'outline'}>{formattedDate}</Badge>;
    },
    size: 200,
    enableSorting: true,
  },
];

interface VehiclesDataTableProps {
  className?: string;
}

export function VehiclesDataTable({ className }: VehiclesDataTableProps) {
  const [isCreateDialogOpen, setCreateDialogOpen] = useState(false);

  const handleEditVehicles = (selectedVehicles: IVehicleEntity[]) => {
    console.log('Edit vehicles:', selectedVehicles);
  };

  const handleDeleteVehicles = (selectedVehicles: IVehicleEntity[]) => {
    console.log('Delete vehicles:', selectedVehicles);
  };

  const handleAddVehicle = () => {
    setCreateDialogOpen(true);
  };

  const config: DataTableConfig<IVehicleEntity, VehicleTableSearchParams> = {
    export: {
      enabled: true,
      fileName: 'vehicules',
      sheetName: 'Véhicules',
      excludeColumns: ['select', 'actions'],
      columnMap: {
        licensePlate: 'Immatriculation',
        model: 'Modèle',
        insuranceNumber: "Numéro d'assurance",
        technicalControlExpiryDate: "Date d'expiration contrôle technique",
      },
      valueTransformers: {
        licensePlate: (value) => (value as string) || '',
        model: (value) => (value as string) || 'Aucun modèle',
        insuranceNumber: (value) => (value as string) || 'Aucun numéro',
        technicalControlExpiryDate: (value) => {
          if (!value) return 'Non définie';
          const date = new Date(value as string);
          const formattedDate = date.toLocaleDateString('fr-FR');
          const isExpired = date < new Date();
          return isExpired ? `${formattedDate} (EXPIRÉ)` : formattedDate;
        },
      },
    },
    columns,
    search: {
      enabled: true,
      placeholder: 'Rechercher un véhicule...',
      searchKey: 'search',
    },
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Modifier',
        icon: <EditIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleEditVehicles,
        variant: 'outline',
        requiresSelection: true,
      },
      {
        label: 'Supprimer',
        icon: <TrashIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleDeleteVehicles,
        variant: 'destructive',
        requiresSelection: true,
      },
      {
        label: 'Ajouter un véhicule',
        icon: <PlusIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleAddVehicle,
        variant: 'outline',
        requiresSelection: false,
      },
    ],
  };

  return (
    <>
      <PaginatedDataTable
        queryKey={['vehicles', 'paginated']}
        queryFn={(params) => vehicleManagerApiService.findAll(params)}
        config={config}
        className={className}
      />
      <Dialog open={isCreateDialogOpen} onOpenChange={setCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Créer un nouveau véhicule</DialogTitle>
          </DialogHeader>
          <VehicleForm onSuccess={() => setCreateDialogOpen(false)} />
        </DialogContent>
      </Dialog>
    </>
  );
}

export default VehiclesDataTable;
