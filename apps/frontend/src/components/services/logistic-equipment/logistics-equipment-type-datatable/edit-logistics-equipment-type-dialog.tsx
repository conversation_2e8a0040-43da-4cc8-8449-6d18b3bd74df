import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import type { ILogisticsEquipmentTypeEntity } from '@/interfaces/entity/i-logistics-equipment-type-entity';
import { EditLogisticsEquipmentTypeForm } from '../logistics-equipment-type-form/edit-logistics-equipment-type-form';
import { logisticsEquipmentTypeManagerApiService } from '@/lib/api/service/logistics-equipment-type-manager-api-service';
import type { IUpdateLogisticsEquipmentTypeDto } from '@/lib/dto/logistics-equipment-type.dto';

interface EditLogisticsEquipmentTypeDialogProps {
  equipmentType: ILogisticsEquipmentTypeEntity | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function EditLogisticsEquipmentTypeDialog({
  equipmentType,
  open,
  onOpenChange,
}: EditLogisticsEquipmentTypeDialogProps) {
  const queryClient = useQueryClient();

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: IUpdateLogisticsEquipmentTypeDto }) =>
      logisticsEquipmentTypeManagerApiService.update(id, data),
    onSuccess: () => {
      toast.success("Type d'équipement modifié avec succès");
      queryClient.invalidateQueries({ queryKey: ['logistics-equipment-types'] });
      onOpenChange(false);
    },
    onError: (error: unknown) => {
      const message =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data
          ? String(error.response.data.message)
          : 'Une erreur est survenue';
      toast.error(message);
    },
  });

  if (!equipmentType) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Modifier le type d'équipement</DialogTitle>
          <DialogDescription>
            Modifiez les informations du type d'équipement {equipmentType.name}.
          </DialogDescription>
        </DialogHeader>

        <EditLogisticsEquipmentTypeForm
          logisticsEquipmentType={equipmentType}
          onSubmit={(data) => updateMutation.mutate({ id: equipmentType.id, data })}
          isLoading={updateMutation.isPending}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
