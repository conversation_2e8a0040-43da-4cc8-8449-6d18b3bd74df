import type { IStopEntity } from '@/interfaces/entity/i-stop-entity.ts';
import type { IPaginatedResponse, IPaginationParams } from '@/interfaces/pagination.ts';
import { keycloakService } from '@/lib/services/keycloak.service.ts';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { stopManagerApiService } from '@/lib/api/service/stop-manager-api-service.ts';

export const stopQueryKeys = {
  all: ['stops'] as const,
  paginated: () => [...stopQueryKeys.all, 'paginated'] as const,
  byDate: (date: Date) => [...stopQueryKeys.paginated(), format(date, 'yyyy-MM-dd')] as const,
  today: () => [...stopQueryKeys.paginated(), 'today'] as const,
  byDateRange: (startDate: Date, endDate: Date) =>
    [
      ...stopQueryKeys.paginated(),
      format(startDate, 'yyyy-MM-dd'),
      format(endDate, 'yyyy-MM-dd'),
    ] as const,
};

export const useStopsByDateQuery = (date: Date, params?: IPaginationParams) => {
  return useQuery({
    queryKey: [...stopQueryKeys.byDate(date), params],
    queryFn: async (): Promise<IPaginatedResponse<IStopEntity>> => {
      if (!keycloakService.keycloak.authenticated) {
        return {
          items: [],
          meta: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        };
      }
      return stopManagerApiService.getStopsByDeliveryDate(format(date, 'yyyy-MM-dd'), params);
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60, // 1 minute
  });
};

export const useTodayStopsQuery = (params?: IPaginationParams) => {
  return useQuery({
    queryKey: [...stopQueryKeys.today(), params],
    queryFn: async (): Promise<IPaginatedResponse<IStopEntity>> => {
      if (!keycloakService.keycloak.authenticated) {
        return {
          items: [],
          meta: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        };
      }
      return stopManagerApiService.getTodayStops(params);
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60, // 1 minute
  });
};

export const useStopsByDateRangeQuery = (
  startDate: Date,
  endDate: Date,
  params?: IPaginationParams,
) => {
  return useQuery({
    queryKey: [...stopQueryKeys.byDateRange(startDate, endDate), params],
    queryFn: async (): Promise<IPaginatedResponse<IStopEntity>> => {
      if (!keycloakService.keycloak.authenticated) {
        return {
          items: [],
          meta: {
            page: 1,
            limit: 10,
            totalItems: 0,
            totalPages: 0,
            hasNextPage: false,
            hasPreviousPage: false,
          },
        };
      }
      return stopManagerApiService.getStopsByDateRange(
        format(startDate, 'yyyy-MM-dd'),
        format(endDate, 'yyyy-MM-dd'),
        params,
      );
    },
    enabled: keycloakService.keycloak.authenticated,
    staleTime: 1000 * 60, // 1 minute
  });
};
