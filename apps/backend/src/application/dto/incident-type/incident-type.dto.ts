import { PartialType } from '@nestjs/mapped-types';
import { IsBoolean, IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class CreateIncidentTypeDto {
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  name: string;

  @IsNotEmpty()
  @IsBoolean()
  doesCancelDelivery: boolean;

  @IsNotEmpty()
  @IsBoolean()
  doesSetPartialDelivery: boolean;
}

export class UpdateIncidentTypeDto extends PartialType(CreateIncidentTypeDto) {}
