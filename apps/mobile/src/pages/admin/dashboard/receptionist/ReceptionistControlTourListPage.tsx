import { useHistory } from 'react-router-dom';
import { Page } from '../../../../components/layout/Page';
import { TourCard } from '../../../../components/ui/shared/TourCard';
import { PATHS } from '../../../../config/routes';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { receptionistReturnControlSelectors } from '../../../../stores/receptionistReturnControlSlice';
import { useAppSelector } from '../../../../utils/redux';

export const ReceptionistControlTourListPage: React.FC = () => {
  const toursEligibleForReturnControl = useAppSelector(
    receptionistReturnControlSelectors.toursEligibleForReturnControl,
  );
  const isDataResolved = useAppSelector((state) => state.tour.isDataResolved);
  const history = useHistory();

  const handleTourClick = (tour: ITourEntity) => {
    history.push(PATHS.RETURN_CONTROL_TOUR.replace(':id', tour.id));
  };

  if (!isDataResolved) {
    return <div>Loading...</div>;
  }

  // TODO A restyliser
  return (
    <Page>
      <div className="flex flex-col gap-4 p-4">
        {toursEligibleForReturnControl.map((tour) => (
          <TourCard key={tour.id} tour={tour} onClick={() => handleTourClick(tour)} />
        ))}
      </div>
    </Page>
  );
};
