'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DatePicker } from '@/components/ui/date-picker';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { ITourEntity } from '@/interfaces/entity/i-tour-entity';
import { TourStatus } from '@/interfaces/enum/tour.enums';
import type { IPaginationParams } from '@/interfaces/pagination';
import { tourManagerApiService } from '@/lib/api/service/tour-manager-api-service';
import { ColumnDef } from '@tanstack/react-table';
import { CalendarIcon, EyeIcon, MapPinIcon, PackageIcon, RotateCcwIcon } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';

interface TourTableSearchParams extends Record<string, unknown> {
  date?: string;
  tourNumber?: string;
}

interface ToursDataTableProps {
  className?: string;
}

export function ToursDataTable({ className }: ToursDataTableProps) {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // Récupérer les valeurs des filtres depuis l'URL ou utiliser des valeurs par défaut
  const selectedDate = searchParams.get('date') || new Date().toISOString().split('T')[0];
  const tourNumber = searchParams.get('tourNumber') || '';

  // Fonctions pour mettre à jour les filtres dans l'URL
  const updateSearchParams = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    setSearchParams(newParams);
  };

  const setSelectedDate = (date: string) => updateSearchParams('date', date);
  const setTourNumber = (number: string) => updateSearchParams('tourNumber', number);

  const handleViewTour = (tour: ITourEntity) => {
    navigate(`/tours/${tour.id}`);
  };

  const resetFilters = () => {
    setSearchParams(new URLSearchParams({ date: new Date().toISOString().split('T')[0] }));
  };

  const handleViewTours = (selectedTours: ITourEntity[]) => {
    console.log('View tours:', selectedTours);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const getStatusColor = (status: TourStatus) => {
    switch (status) {
      case TourStatus.Completed:
        return 'default';
      case TourStatus.InProgress:
        return 'secondary';
      case TourStatus.Planned:
        return 'outline';
      default:
        return 'outline';
    }
  };

  const getStatusLabel = (status: TourStatus) => {
    switch (status) {
      case TourStatus.Completed:
        return 'Terminée';
      case TourStatus.InProgress:
        return 'En cours';
      case TourStatus.Planned:
        return 'Planifiée';
      default:
        return status;
    }
  };

  const getTourProgress = (tour: ITourEntity): number => {
    if (!tour.stops || tour.stops.length === 0) return 0;

    const completedStops = tour.stops.filter((stop) => stop.completion?.completedAt).length;
    return Math.round((completedStops / tour.stops.length) * 100);
  };

  const columns: ColumnDef<ITourEntity>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Tout sélectionner"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Sélectionner la ligne"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: 'Tournée',
      accessorKey: 'tourIdentifier.originalNumber',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <div className="bg-primary/10 p-1.5 rounded">
            <PackageIcon className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="font-medium">
              {row.original.tourIdentifier?.originalNumber || row.original.id.slice(0, 8)}
            </div>
            <div className="text-sm text-muted-foreground">
              Type: {row.original.tourIdentifier?.type || 'NORMAL'}
            </div>
          </div>
        </div>
      ),
      size: 180,
      enableHiding: false,
    },
    {
      header: 'Date de livraison',
      accessorKey: 'deliveryDate',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          <span>{formatDate(row.original.deliveryDate)}</span>
        </div>
      ),
      size: 150,
    },
    {
      header: 'Progression',
      accessorKey: 'status',
      cell: ({ row }) => {
        const progress = getTourProgress(row.original);
        const status = row.original.status;

        return (
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <Badge variant={getStatusColor(status)} className="text-xs">
                {getStatusLabel(status)}
              </Badge>
              <span className="text-xs text-muted-foreground">{progress}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        );
      },
      size: 160,
      enableSorting: true,
    },
    {
      header: 'Arrêts',
      accessorKey: 'stops',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <MapPinIcon className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{row.original.stops?.length || 0}</span>
          <span className="text-sm text-muted-foreground">arrêts</span>
        </div>
      ),
      size: 100,
      enableSorting: false,
    },
    {
      header: 'Fichier source',
      accessorKey: 'providerFileName',
      cell: ({ row }) => (
        <div className="max-w-48 truncate text-sm text-muted-foreground">
          {row.original.providerFileName || 'Non défini'}
        </div>
      ),
      size: 200,
      enableSorting: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewTour(row.original)}
          className="h-8 w-8 p-0"
        >
          <EyeIcon className="h-4 w-4" />
          <span className="sr-only">Voir les détails de la tournée</span>
        </Button>
      ),
      size: 80,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<ITourEntity, TourTableSearchParams> = {
    export: {
      enabled: true,
      fileName: 'tournees',
      sheetName: 'Tournées',
      excludeColumns: ['select', 'actions'],
      columnMap: {
        'tourIdentifier.originalNumber': 'Numéro de tournée',
        deliveryDate: 'Date de livraison',
        status: 'Progression',
        stops: "Nombre d'arrêts",
        providerFileName: 'Fichier source',
      },
      valueTransformers: {
        status: (value) => getStatusLabel(value as TourStatus),
        deliveryDate: (value) => {
          const date = new Date(value as string);
          return date.toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
          });
        },
        stops: (value) => (Array.isArray(value) ? value.length : 0),
        providerFileName: (value) => (value as string) || 'Non défini',
      },
    },
    columns,
    filters: [
      {
        columnKey: 'status',
        label: 'Statut',
        type: 'select',
        options: [
          { label: 'Tous les statuts', value: 'all' },
          { label: 'Planifiée', value: TourStatus.Planned },
          { label: 'En cours', value: TourStatus.InProgress },
          { label: 'Terminée', value: TourStatus.Completed },
        ],
        placeholder: 'Filtrer par statut',
      },
    ],
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Voir détails',
        icon: <EyeIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleViewTours,
        variant: 'outline',
        requiresSelection: true,
      },
    ],
  };

  const customQueryFn = (params: IPaginationParams & TourTableSearchParams) => {
    // Ajouter le filtre par numéro de tournée aux paramètres
    const queryParams = {
      ...params,
      ...(tourNumber && { tourOriginalNumber: tourNumber }),
    };

    if (selectedDate) {
      return tourManagerApiService.getToursByDate(selectedDate, queryParams);
    }
    return tourManagerApiService.getTodayTours(queryParams);
  };

  return (
    <div className="space-y-4">
      {/* Filtres */}
      <div className="flex items-center gap-4 flex-wrap">
        <div className="flex items-center gap-2">
          <Label htmlFor="date-filter" className="text-sm font-medium">
            Date de livraison :
          </Label>
          <DatePicker
            value={selectedDate}
            onChange={setSelectedDate}
            placeholder="Sélectionner une date de livraison"
            className="min-w-[200px]"
          />
        </div>

        <div className="flex items-center gap-2">
          <Label htmlFor="tour-number-filter" className="text-sm font-medium">
            N° de tournée :
          </Label>
          <Input
            id="tour-number-filter"
            type="text"
            value={tourNumber}
            onChange={(e) => setTourNumber(e.target.value)}
            placeholder="Rechercher par numéro..."
            className="min-w-[200px]"
          />
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={resetFilters}
          className="flex items-center gap-2"
        >
          <RotateCcwIcon className="h-4 w-4" />
          Réinitialiser
        </Button>
      </div>

      <PaginatedDataTable
        queryKey={['tours', 'paginated', selectedDate, tourNumber]}
        queryFn={customQueryFn}
        config={config}
        className={className}
      />
    </div>
  );
}

export default ToursDataTable;
