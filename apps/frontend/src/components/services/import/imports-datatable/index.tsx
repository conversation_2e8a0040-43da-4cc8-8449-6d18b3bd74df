'use client';

import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { DatePicker } from '@/components/ui/date-picker';
import { Label } from '@/components/ui/label';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { IImportEntity } from '@/interfaces/entity/i-import-entity';
import type { IPaginationParams } from '@/interfaces/pagination';
import { importManagerApiService } from '@/lib/api/service/import-manager-api-service';
import { ColumnDef } from '@tanstack/react-table';
import {
  CalendarIcon,
  CheckCircleIcon,
  EyeIcon,
  FileIcon,
  PlayIcon,
  RotateCcwIcon,
  XCircleIcon,
} from 'lucide-react';
import { useState } from 'react';
import { useSearchParams } from 'react-router';
import { ImportStatus } from '../../../../interfaces/enum/import-status.enum';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';
import ImportDetailsModal from '../import-details-modal';

interface ImportTableSearchParams extends Record<string, unknown> {
  startDate?: string;
  endDate?: string;
  status?: string;
}

interface ImportsDataTableProps {
  className?: string;
  onTriggerImport?: () => void;
}

export function ImportsDataTable({ className, onTriggerImport }: ImportsDataTableProps) {
  const [searchParams, setSearchParams] = useSearchParams();

  const startDate = searchParams.get('startDate') || '';
  const endDate = searchParams.get('endDate') || '';
  const status = searchParams.get('status') || '';

  const updateSearchParams = (key: string, value: string) => {
    const newParams = new URLSearchParams(searchParams);
    if (value) {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
    setSearchParams(newParams);
  };

  const setStartDate = (date: string) => updateSearchParams('startDate', date);
  const setEndDate = (date: string) => updateSearchParams('endDate', date);
  const setStatus = (statusValue: string) => updateSearchParams('status', statusValue);

  const resetFilters = () => {
    setSearchParams(new URLSearchParams());
  };

  const [selectedImportId, setSelectedImportId] = useState<string | undefined>();
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const handleViewImport = (importEntity: IImportEntity) => {
    setSelectedImportId(importEntity.id);
    setIsDetailsModalOpen(true);
  };

  const handleViewImports = (selectedImports: IImportEntity[]) => {
    if (selectedImports.length === 1) {
      handleViewImport(selectedImports[0]);
    } else if (selectedImports.length > 1) {
      handleViewImport(selectedImports[0]);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getStatusColor = (status: ImportStatus) => {
    switch (status) {
      case ImportStatus.Completed:
        return 'default';
      case ImportStatus.Failed:
        return 'destructive';
      case ImportStatus.PartiallyCompleted:
        return 'secondary';
      case ImportStatus.Skipped:
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusLabel = (status: ImportStatus) => {
    switch (status) {
      case ImportStatus.Completed:
        return 'Réussi';
      case ImportStatus.Failed:
        return 'Échec';
      case ImportStatus.PartiallyCompleted:
        return 'Partiellement réussi';
      default:
        return status || 'Inconnu';
    }
  };

  const columns: ColumnDef<IImportEntity>[] = [
    {
      header: 'Import',
      accessorKey: 'id',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <div className="bg-primary/10 p-1.5 rounded">
            <FileIcon className="h-4 w-4 text-primary" />
          </div>
          <div>
            <div className="font-medium">#{row.original.id.slice(0, 8)}</div>
            <div className="text-sm text-muted-foreground">Import #{row.original.id.slice(-4)}</div>
          </div>
        </div>
      ),
      size: 180,
      enableHiding: false,
    },
    {
      header: 'Date de création',
      accessorKey: 'createdAt',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          <span>{formatDate(row.original.createdAt)}</span>
        </div>
      ),
      size: 180,
    },
    {
      header: 'Statut',
      accessorKey: 'status',
      cell: ({ row }) => {
        const status = row.original.status;
        return (
          <div className="flex items-center gap-2">
            <Badge variant={getStatusColor(status)} className="text-xs">
              {getStatusLabel(status)}
            </Badge>
          </div>
        );
      },
      size: 120,
      enableSorting: true,
    },
    {
      header: 'Enregistrements réussis',
      accessorKey: 'successfulRecords',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <CheckCircleIcon className="h-4 w-4 text-green-600" />
          <span className="font-medium">{row.original.successfulRecords || 0}</span>
          <span className="text-sm text-muted-foreground">réussis</span>
        </div>
      ),
      size: 120,
      enableSorting: false,
    },
    {
      header: 'Enregistrements échoués',
      accessorKey: 'failedRecords',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <XCircleIcon className="h-4 w-4 text-red-600" />
          <span className="font-medium">{row.original.failedRecords || 0}</span>
          <span className="text-sm text-muted-foreground">échoués</span>
        </div>
      ),
      size: 120,
      enableSorting: false,
    },
    {
      header: "Date d'import",
      accessorKey: 'importDate',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          <span>{formatDate(row.original.importDate)}</span>
        </div>
      ),
      size: 180,
      enableSorting: true,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewImport(row.original)}
          className="h-8 w-8 p-0"
        >
          <EyeIcon className="h-4 w-4" />
          <span className="sr-only">Voir les détails de l'import</span>
        </Button>
      ),
      size: 80,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<IImportEntity, ImportTableSearchParams> = {
    export: {
      enabled: true,
      fileName: 'imports',
      sheetName: 'Historique des imports',
      excludeColumns: ['select', 'actions'],
      columnMap: {
        id: 'ID Import',
        createdAt: 'Date de création',
        status: 'Statut',
        successfulRecords: 'Enregistrements réussis',
        failedRecords: 'Enregistrements échoués',
        importDate: "Date d'import",
      },
      valueTransformers: {
        status: (value) => getStatusLabel(value as ImportStatus),
        createdAt: (value) => formatDate(value as string),
        successfulRecords: (value) => (value as number) || 0,
        failedRecords: (value) => (value as number) || 0,
        importDate: (value) => formatDate(value as string),
      },
    },
    columns,
    filters: [
      {
        columnKey: 'status',
        label: 'Statut',
        type: 'select',
        options: [
          { label: 'Tous les statuts', value: 'all' },
          { label: 'Réussi', value: 'COMPLETED' },
          { label: 'Échec', value: 'FAILED' },
          { label: 'Partiellement réussi', value: 'PARTIALLY_COMPLETED' },
          { label: 'Ignoré', value: 'SKIPPED' },
        ],
        placeholder: 'Filtrer par statut',
      },
    ],
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Voir détails',
        icon: <EyeIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleViewImports,
        variant: 'outline',
        requiresSelection: true,
      },
    ],
  };

  const customQueryFn = (params: IPaginationParams & ImportTableSearchParams) => {
    const queryParams = {
      ...params,
      ...(startDate && { startDate }),
      ...(endDate && { endDate }),
      ...(status && status !== 'all' && { status }),
    };

    return importManagerApiService.getImportList(queryParams);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4 flex-wrap">
          <div className="flex items-center gap-2">
            <Label htmlFor="start-date-filter" className="text-sm font-medium">
              Date de début :
            </Label>
            <DatePicker
              value={startDate}
              onChange={setStartDate}
              placeholder="Date de début"
              className="min-w-[150px]"
            />
          </div>

          <div className="flex items-center gap-2">
            <Label htmlFor="end-date-filter" className="text-sm font-medium">
              Date de fin :
            </Label>
            <DatePicker
              value={endDate}
              onChange={setEndDate}
              placeholder="Date de fin"
              className="min-w-[150px]"
            />
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={resetFilters}
            className="flex items-center gap-2"
          >
            <RotateCcwIcon className="h-4 w-4" />
            Réinitialiser
          </Button>
        </div>

        {onTriggerImport && (
          <Button onClick={onTriggerImport} className="flex items-center gap-2">
            <PlayIcon className="h-4 w-4" />
            Déclencher un import
          </Button>
        )}
      </div>

      <PaginatedDataTable
        queryKey={['imports', 'paginated', startDate, endDate, status]}
        queryFn={customQueryFn}
        config={config}
        className={className}
      />

      <ImportDetailsModal
        open={isDetailsModalOpen}
        onOpenChange={setIsDetailsModalOpen}
        importId={selectedImportId}
      />
    </div>
  );
}

export default ImportsDataTable;
