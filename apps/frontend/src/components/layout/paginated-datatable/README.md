# PaginatedDataTable

Un système d'adaptateur réutilisable pour connecter les endpoints paginés du backend avec des datatables interactives.

## Utilisation de base

```tsx
import { PaginatedDataTable } from '@/components/layout/paginated-datatable';
import { userApiService } from '@/lib/api/service/user-api-service';

const config = {
  columns: userColumns,
  search: { enabled: true, searchKey: 'search' },
  enableSelection: true,
  actions: [editAction, deleteAction]
};

<PaginatedDataTable
  queryKey={['users', 'paginated']}
  queryFn={(params) => userApiService.getUsers(params)}
  config={config}
/>
```

## Configuration

### Colonnes
Utilise les `ColumnDef` de TanStack Table pour définir les colonnes.

### Recherche côté serveur
```tsx
search: {
  enabled: true, // Active la barre de recherche
  placeholder: 'Search users...',
  searchKey: 'search' // Paramètre envoyé au backend
}
```

### Actions
```tsx
actions: [
  {
    label: 'Edit',
    icon: <EditIcon />,
    onClick: (selectedRows) => handleEdit(selectedRows),
    requiresSelection: true,
    variant: 'outline'
  }
]
```

## Fonctionnalités

- ✅ **Pagination serveur** via IPaginationParams (page, limit, sortBy, sortOrder)
- ✅ **Tri serveur** automatique via les en-têtes de colonnes
- ✅ **Sélection multiple** avec actions en lot
- ✅ **Loading states** et gestion d'erreurs
- ✅ **Colonnes masquables**
- ⚠️ **Recherche serveur** (nécessite implémentation backend)

## TODO Backend

Pour activer la recherche côté serveur, les endpoints doivent être mis à jour :

### 1. Étendre PaginationParamsDto
```typescript
export class PaginationParamsDto {
  // ... existing fields
  
  @IsOptional()
  @IsString()
  search?: string;
}
```

### 2. Mettre à jour les services
```typescript
async findAllPaginated(
  pagination: PaginationParamsDto,
  searchTerm?: string
): Promise<{ items: UserEntity[]; meta: PaginationMetaDto }> {
  const whereClause = searchTerm 
    ? [
        { username: ILike(`%${searchTerm}%`) },
        { email: ILike(`%${searchTerm}%`) }
      ]
    : {};
    
  return this.paginationAdapter.paginate(pagination, {
    where: whereClause
  });
}
```

### 3. Mettre à jour les contrôleurs
```typescript
@Get()
async listUsers(
  @PaginationQuery() pagination: PaginationParamsDto,
  @Query('search') search?: string
): Promise<PaginatedResponseDto<UserEntity>> {
  const { items, meta } = await this.userService.findAllPaginated(pagination, search);
  return new ListBuilder(UserEntity).items(items).meta(meta).build();
}
```