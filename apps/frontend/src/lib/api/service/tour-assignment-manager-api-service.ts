import type { ITourAssignmentEntity } from '../../../interfaces/entity/i-tour-assignment-entity.ts';
import type { IPaginatedResponse, IPaginationParams } from '../../../interfaces/pagination.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';

export class TourAssignmentManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getTourAssignments(
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ITourAssignmentEntity>> {
    const response = await this.apiClient.get('/api/manager/tour-assignments', { params });
    return response.data;
  }

  async getTourAssignmentById(id: string): Promise<ITourAssignmentEntity> {
    const response = await this.apiClient.get(`/api/manager/tour-assignments/${id}`);
    return response.data;
  }

  async createTourAssignment(data: {
    userId: string;
    tourId: string;
    startDate: string;
    endDate?: string;
  }): Promise<ITourAssignmentEntity> {
    const response = await this.apiClient.post('/api/manager/tour-assignments', data);
    return response.data;
  }

  async updateTourAssignment(
    id: string,
    data: {
      userId?: string;
      tourId?: string;
      startDate?: string;
      endDate?: string;
    },
  ): Promise<ITourAssignmentEntity> {
    const response = await this.apiClient.patch(`/api/manager/tour-assignments/${id}`, data);
    return response.data;
  }

  async deleteTourAssignment(id: string): Promise<void> {
    await this.apiClient.delete(`/api/manager/tour-assignments/${id}`);
  }
}

export const tourAssignmentManagerApiService = new TourAssignmentManagerApiService(apiClient);
