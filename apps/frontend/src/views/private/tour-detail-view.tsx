import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { TourHeader } from '@/components/services/tour/tour-header';
import { StopsTable } from '@/components/services/tour/stops-table';
import { IncidentsTable } from '@/components/services/tour/incidents-table';
import { TourTimeline } from '@/components/services/tour/tour-timeline';
import { useTourByIdQuery } from '@/hooks/queries/tour-detail-queries';
import { ArrowLeft } from 'lucide-react';
import { useNavigate, useParams } from 'react-router';

export function TourDetailView() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const tourQuery = useTourByIdQuery(id!);

  if (tourQuery.isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-muted-foreground">Chargement de la tournée...</div>
        </div>
      </div>
    );
  }

  if (tourQuery.isError) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-destructive">Erreur lors du chargement de la tournée</div>
        </div>
      </div>
    );
  }

  const tour = tourQuery.data;

  return (
    <div className="p-6">
      <div className="mb-6">
        <Button variant="ghost" className="mb-4" onClick={() => navigate('/tour')}>
          <ArrowLeft className="-ms-1 opacity-60" size={16} aria-hidden="true" />
          Retour aux tournées
        </Button>

        <TourHeader tour={tour} />
      </div>

      <Tabs defaultValue="stops" className="space-y-4">
        <TabsList>
          <TabsTrigger value="stops">Arrêts</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="incidents">Incidents</TabsTrigger>
        </TabsList>

        <TabsContent value="stops">
          <StopsTable tourId={id!} />
        </TabsContent>

        <TabsContent value="timeline">
          <TourTimeline tourId={id!} />
        </TabsContent>

        <TabsContent value="incidents">
          <IncidentsTable tourId={id!} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
