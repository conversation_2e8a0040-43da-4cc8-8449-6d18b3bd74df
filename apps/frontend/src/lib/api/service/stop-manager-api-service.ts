import type { IStopEntity } from '../../../interfaces/entity/i-stop-entity.ts';
import type { IPaginatedResponse, IPaginationParams } from '../../../interfaces/pagination.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';

export class StopManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getStopsByDeliveryDate(
    date: string,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<IStopEntity>> {
    const response = await this.apiClient.get(`/api/manager/stops/by-date/${date}`, { params });
    return response.data;
  }

  async getTodayStops(params?: IPaginationParams): Promise<IPaginatedResponse<IStopEntity>> {
    const response = await this.apiClient.get('/api/manager/stops/today', { params });
    return response.data;
  }

  async getStopsByDateRange(
    startDate: string,
    endDate: string,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<IStopEntity>> {
    const response = await this.apiClient.get('/api/manager/stops/by-date-range', {
      params: {
        ...params,
        startDate,
        endDate,
      },
    });
    return response.data;
  }
}

export const stopManagerApiService = new StopManagerApiService(apiClient);
