import { LogisticsEquipmentKind } from '../../../../interfaces/enum/logistics-equipment-kind.enum';
import { receptionistReturnControlActions } from '../../../../stores/receptionistReturnControlSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import InlineButtons from '../../../ui/stylized/InlineButtons';

export function ControlTourFooter() {
  const currentStep = useAppSelector((state) => state.receptionistReturnControl.currentStep);
  const dispatch = useAppDispatch();

  const handleNextStep = () => {
    if (currentStep === LogisticsEquipmentKind.PALLET) {
      console.log('🔍 [ControlTourFooter.handleNextStep] Submitting...');

      // TODO submit
      return;
    }

    dispatch(receptionistReturnControlActions.nextStep());
  };

  return (
    <div className="absolute bottom-6 left-1/2 right-1/2 w-full max-w-3xl transform -translate-x-1/2  px-4 z-20 ">
      <InlineButtons
        buttons={[
          {
            label: 'Suivant',
            onClick: handleNextStep,
            classNames: {
              button: `px-8 sm:px-12 py-5 sm:py-4 primary-button opacity-100`,
            },
            disabled: false,
          },
        ]}
      />
    </div>
  );
}
