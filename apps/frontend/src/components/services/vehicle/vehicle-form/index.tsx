'use client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { IVehicleEntity } from '@/interfaces/entity/i-vehicle-entity';
import { vehicleManagerApiService } from '@/lib/api/service/vehicle-manager-api-service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

interface VehicleFormProps {
  onSuccess?: () => void;
}

export function VehicleForm({ onSuccess }: VehicleFormProps) {
  const queryClient = useQueryClient();
  const [licensePlate, setLicensePlate] = useState('');
  const [model, setModel] = useState('');
  const [insuranceNumber, setInsuranceNumber] = useState('');
  const [technicalControlExpiryDate, setTechnicalControlExpiryDate] = useState('');
  const [error, setError] = useState('');

  const mutation = useMutation({
    mutationFn: (newVehicle: Omit<IVehicleEntity, 'id' | 'createdAt' | 'updatedAt'>) =>
      vehicleManagerApiService.create(newVehicle),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vehicles', 'paginated'] });
      onSuccess?.();
    },
    onError: (error: Error & { message?: string }) => {
      setError(error.message || 'Une erreur est survenue lors de la création du véhicule.');
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!licensePlate.trim()) {
      setError("La plaque d'immatriculation est requise.");
      return;
    }
    setError('');

    const newVehicle: Omit<IVehicleEntity, 'id' | 'createdAt' | 'updatedAt'> = {
      licensePlate,
      model: model || undefined,
      insuranceNumber: insuranceNumber || undefined,
      technicalControlExpiryDate: technicalControlExpiryDate || undefined,
    };
    mutation.mutate(newVehicle);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="grid gap-2">
        <Label htmlFor="licensePlate">Plaque d'immatriculation</Label>
        <Input
          id="licensePlate"
          value={licensePlate}
          onChange={(e) => setLicensePlate(e.target.value)}
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="model">Modèle</Label>
        <Input id="model" value={model} onChange={(e) => setModel(e.target.value)} />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="insuranceNumber">Numéro d'assurance</Label>
        <Input
          id="insuranceNumber"
          value={insuranceNumber}
          onChange={(e) => setInsuranceNumber(e.target.value)}
        />
      </div>
      <div className="grid gap-2">
        <Label htmlFor="technicalControlExpiryDate">Date d'expiration du contrôle technique</Label>
        <Input
          id="technicalControlExpiryDate"
          type="date"
          value={technicalControlExpiryDate}
          onChange={(e) => setTechnicalControlExpiryDate(e.target.value)}
        />
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
      <Button type="submit" disabled={mutation.isPending} className="w-full">
        {mutation.isPending ? 'Création...' : 'Créer le véhicule'}
      </Button>
    </form>
  );
}
