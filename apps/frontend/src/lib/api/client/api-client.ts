import type { AxiosInstance } from 'axios';
import axios from 'axios';
import { getEnv } from '../../get-env.utils.ts';
import { keycloakService } from '../../services/keycloak.service.ts';

export class ApiClient {
  private instance: AxiosInstance;
  private baseUrl: string;

  constructor(
    baseUrl: string,
    private readonly getToken: () => Promise<string | undefined>,
  ) {
    this.baseUrl = baseUrl;
    this.instance = axios.create({
      baseURL: baseUrl,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    });

    // Handle JSON responses automatically
    this.instance.interceptors.response.use((response) => {
      return response;
    });

    // Interceptor to inject the Authorization token if present
    this.instance.interceptors.request.use(async (config) => {
      const token = await this.getToken();
      if (token) {
        config.headers['Authorization'] = `Bearer ${token}`;
      }
      return config;
    });
  }

  getBaseUrl(): string {
    return this.baseUrl;
  }

  // Expose standard HTTP methods
  async get(url: string, config = {}) {
    return this.instance.get(url, config);
  }

  async post(url: string, data = {}, config = {}) {
    return this.instance.post(url, data, config);
  }

  async put(url: string, data = {}, config = {}) {
    return this.instance.put(url, data, config);
  }

  async delete(url: string, config = {}) {
    return this.instance.delete(url, config);
  }

  async patch(url: string, data = {}, config = {}) {
    return this.instance.patch(url, data, config);
  }

  // Helper for SWR or React Query
  asFetcher = (url: string) => {
    return this.get(url).then((response) => {
      return response.data;
    });
  };
}

export const apiClient = new ApiClient(getEnv('VITE_API_URL') || '', async () => {
  const token = keycloakService.keycloak?.token;

  return token;
});
