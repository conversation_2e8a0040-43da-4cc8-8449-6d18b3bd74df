import { useParams } from 'react-router-dom';
import { Page } from '../../../../components/layout/Page';
import { ReceptionistControlTour } from '../../../../components/services/receptionist/control-tour/ReceptionistControlTour';
import { receptionistReturnControlSelectors } from '../../../../stores/receptionistReturnControlSlice';
import { useAppSelector } from '../../../../utils/redux';

export function ControlTourPage() {
  const { id } = useParams<{ id: string }>();
  const eligibleTours = useAppSelector(
    receptionistReturnControlSelectors.toursEligibleForReturnControl,
  );
  const isDataResolved = useAppSelector((state) => state.tour.isDataResolved);

  // TODO a restyliser

  if (!isDataResolved) {
    return <div>Loading...</div>;
  }

  const tour = eligibleTours.find((tour) => tour.id === id);

  if (!tour) {
    return <div>Tour not found</div>;
  }

  return (
    <Page>
      <ReceptionistControlTour tour={tour} />
    </Page>
  );
}
