import { LogisticsEquipmentKind } from '../../../../interfaces/enum/logistics-equipment-kind.enum';
import { receptionistReturnControlSelectors } from '../../../../stores/receptionistReturnControlSlice';
import { useAppSelector } from '../../../../utils/redux';
import { EquipmentDetailsItem } from './EquipmentDetailsItem';

const EquipmentKindTitleMap = {
  [LogisticsEquipmentKind.PACKAGE]: 'Colis',
  [LogisticsEquipmentKind.ROLL]: 'Rouleau',
  [LogisticsEquipmentKind.PALLET]: 'Palette',
};

export function EquipmentDetailsList() {
  const currentStep = useAppSelector((state) => state.receptionistReturnControl.currentStep);
  const logisticEquipmentsTypesForCurrentStep = useAppSelector(
    receptionistReturnControlSelectors.logisticEquipmentsTypesForCurrentStep,
  );

  return (
    <div className="px-8">
      <div className=" border border-gray-200 rounded-lg p-4">
        <div>
          <div>{EquipmentKindTitleMap[currentStep]}</div>
        </div>
        <hr className="my-6 h-px bg-gray-200" />
        <div>
          {logisticEquipmentsTypesForCurrentStep.map((equipmentType, index) => (
            <div key={equipmentType.id}>
              <EquipmentDetailsItem equipmentType={equipmentType} />
              {index !== logisticEquipmentsTypesForCurrentStep.length - 1 && (
                <hr className="my-6 h-px bg-gray-200" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
