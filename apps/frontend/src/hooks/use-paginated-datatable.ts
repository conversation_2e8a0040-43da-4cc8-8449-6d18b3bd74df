import {
  ColumnFiltersState,
  getCoreRowModel,
  PaginationState,
  RowSelectionState,
  SortingState,
  useReactTable,
  VisibilityState,
} from '@tanstack/react-table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDebounce } from './use-debounce.ts';
import type { DataTableConfig, DataTableState } from '../interfaces/datatable.ts';
import type { IPaginatedResponse, IPaginationParams } from '../interfaces/pagination.ts';
import { usePaginatedQuery } from './use-paginated-query.ts';

export interface UsePaginatedDataTableOptions<
  TData,
  TSearchParams extends Record<string, unknown> = Record<string, unknown>,
> {
  queryKey: readonly unknown[];
  queryFn: (params: IPaginationParams & TSearchParams) => Promise<IPaginatedResponse<TData>>;
  config: DataTableConfig<TData, TSearchParams>;
  enabled?: boolean;
  debounceMs?: number;
}

export function usePaginatedDataTable<
  TData,
  TSearchParams extends Record<string, unknown> = Record<string, unknown>,
>({
  queryKey,
  queryFn,
  config,
  enabled = true,
  debounceMs = 300,
}: UsePaginatedDataTableOptions<TData, TSearchParams>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});
  const [globalFilter, setGlobalFilter] = useState('');
  const [searchParams, setSearchParams] = useState<TSearchParams>(
    config.searchParams ?? ({} as TSearchParams),
  );

  const [pagination, setPagination] = useState<PaginationState>({
    pageIndex: 0,
    pageSize: config.defaultPageSize ?? 10,
  });

  const debouncedGlobalFilter = useDebounce(globalFilter, debounceMs);
  const debouncedSearchParams = useDebounce(searchParams, debounceMs);

  const queryParams = useMemo((): IPaginationParams & TSearchParams => {
    const sortBy = sorting.length > 0 ? sorting[0]?.id : undefined;
    const sortOrder = sorting.length > 0 ? (sorting[0]?.desc ? 'desc' : 'asc') : undefined;

    const baseParams: IPaginationParams = {
      page: pagination.pageIndex + 1,
      limit: pagination.pageSize,
      ...(sortBy && { sortBy }),
      ...(sortOrder && { sortOrder }),
    };

    const searchObj = {
      ...debouncedSearchParams,
    } as TSearchParams;

    // Ajouter les filtres de colonnes directement comme paramètres
    columnFilters.forEach((filter) => {
      if (filter.value && typeof filter.value === 'string') {
        (searchObj as Record<string, unknown>)[filter.id] = filter.value;
      }
    });

    if (config.search?.enabled && debouncedGlobalFilter && config.search.searchKey) {
      (searchObj as Record<string, unknown>)[config.search.searchKey] = debouncedGlobalFilter;
    }

    return {
      ...baseParams,
      ...searchObj,
    } as IPaginationParams & TSearchParams;
  }, [
    pagination,
    sorting,
    columnFilters,
    debouncedGlobalFilter,
    debouncedSearchParams,
    config.search,
  ]);

  const { data, meta, isLoading, isError, error, isFetching, isPlaceholderData, refetch } =
    usePaginatedQuery({
      queryKey,
      queryFn,
      pagination: queryParams,
      searchParams: {} as TSearchParams,
      enabled,
    });

  const table = useReactTable({
    data,
    columns: config.columns,
    getCoreRowModel: getCoreRowModel(),
    onSortingChange: setSorting,
    enableSortingRemoval: false,
    manualPagination: true,
    manualSorting: true,
    manualFiltering: true,
    pageCount: meta?.totalPages ?? -1,
    onPaginationChange: setPagination,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    onGlobalFilterChange: setGlobalFilter,
    state: {
      sorting,
      pagination,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
  });

  const handleSearchParamsChange = useCallback(
    (newParams: TSearchParams) => {
      setSearchParams(newParams);
      config.onSearchParamsChange?.(newParams);
      setPagination((prev) => ({ ...prev, pageIndex: 0 }));
    },
    [config],
  );

  const handleGlobalFilterChange = useCallback((filter: string) => {
    setGlobalFilter(filter);
    setPagination((prev) => ({ ...prev, pageIndex: 0 }));
  }, []);

  const selectedRowsModel = table.getSelectedRowModel();
  const selectedRows = useMemo(() => {
    return selectedRowsModel.rows.map((row) => row.original);
  }, [selectedRowsModel.rows]);

  useEffect(() => {
    // config.onRowSelectionChange?.(selectedRows);
  }, [selectedRows, config]);

  const tableState: DataTableState<TSearchParams> = {
    pagination: queryParams,
    searchParams: debouncedSearchParams,
    globalFilter: debouncedGlobalFilter,
    columnFilters: columnFilters.reduce(
      (acc, filter) => {
        acc[filter.id] = filter.value;
        return acc;
      },
      {} as Record<string, unknown>,
    ),
  };

  return {
    table,
    data,
    meta,
    isLoading,
    isError,
    error,
    isFetching,
    isPlaceholderData,
    refetch,
    selectedRows,
    globalFilter,
    searchParams,
    tableState,
    handlers: {
      onSearchParamsChange: handleSearchParamsChange,
      onGlobalFilterChange: handleGlobalFilterChange,
    },
  };
}
