import { format } from 'date-fns';
import type { ITourAssignmentEntity } from '../../../interfaces/entity/i-tour-assignment-entity.ts';
import type { ITourEntity } from '../../../interfaces/entity/i-tour-entity.ts';
import type { ITourIdentifier } from '../../../interfaces/entity/i-tour-identifier.ts';
import type { IUserEntity } from '../../../interfaces/entity/i-user-entity.ts';
import { TourType } from '../../../interfaces/enum/tour.enums.ts';
import type { IPaginatedResponse, IPaginationParams } from '../../../interfaces/pagination.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';

export interface CreateTourAssignmentDto {
  tourIdentifier: {
    number: string;
    type: TourType;
    originalNumber: string;
  };
  fromDate: string;
  toDate?: string;
  notes?: string;
  userId: string;
}

export interface UpdateTourAssignmentDto {
  fromDate?: string;
  toDate?: string | null;
  notes?: string;
  userId?: string;
}

export interface PlanningDataParams extends IPaginationParams {
  startDate?: string;
  endDate?: string;
}

export class PlanningApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getToursByDateRange(
    fromDate: Date,
    toDate: Date,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ITourEntity>> {
    const formattedFromDate = format(fromDate, 'yyyy-MM-dd');
    const formattedToDate = format(toDate, 'yyyy-MM-dd');

    const queryParams = {
      ...params,
      startDate: formattedFromDate,
      endDate: formattedToDate,
    };

    const response = await this.apiClient.get('/api/manager/tours/by-date-range', {
      params: queryParams,
    });
    return response.data;
  }

  async getToursByDate(
    date: Date,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ITourEntity>> {
    const formattedDate = format(date, 'yyyy-MM-dd');
    const response = await this.apiClient.get(`/api/manager/tours/by-date/${formattedDate}`, {
      params,
    });
    return response.data;
  }

  async getTourAssignmentsByDateRange(
    fromDate: Date,
    toDate: Date,
    params?: IPaginationParams,
  ): Promise<IPaginatedResponse<ITourAssignmentEntity>> {
    const queryParams = {
      ...params,
      startDate: format(fromDate, 'yyyy-MM-dd'),
      endDate: format(toDate, 'yyyy-MM-dd'),
    };

    const response = await this.apiClient.get('/api/manager/tour-assignments', {
      params: queryParams,
    });
    return response.data;
  }

  async getTourAssignments(
    params?: PlanningDataParams,
  ): Promise<IPaginatedResponse<ITourAssignmentEntity>> {
    const response = await this.apiClient.get('/api/manager/tour-assignments', { params });
    return response.data;
  }

  async getTourAssignmentById(id: string): Promise<ITourAssignmentEntity> {
    const response = await this.apiClient.get(`/api/manager/tour-assignments/${id}`);
    return response.data;
  }

  async createTourAssignment(data: CreateTourAssignmentDto): Promise<ITourAssignmentEntity> {
    const response = await this.apiClient.post('/api/manager/tour-assignments', data);
    return response.data;
  }

  async updateTourAssignment(
    id: string,
    data: UpdateTourAssignmentDto,
  ): Promise<ITourAssignmentEntity> {
    const response = await this.apiClient.patch(`/api/manager/tour-assignments/${id}`, data);
    return response.data;
  }

  async deleteTourAssignment(id: string): Promise<void> {
    await this.apiClient.delete(`/api/manager/tour-assignments/${id}`);
  }

  async getUsersWithRoles(params?: IPaginationParams): Promise<IPaginatedResponse<IUserEntity>> {
    const response = await this.apiClient.get('/api/user', { params });
    return response.data;
  }

  async getTodayTours(params?: IPaginationParams): Promise<IPaginatedResponse<ITourEntity>> {
    const response = await this.apiClient.get('/api/manager/tours/today', { params });
    return response.data;
  }

  async getDistinctTourIdentifiers(startDate?: Date, endDate?: Date): Promise<ITourIdentifier[]> {
    const queryParams: Record<string, string> = {};

    if (startDate) {
      queryParams.startDate = format(startDate, 'yyyy-MM-dd');
    }

    if (endDate) {
      queryParams.endDate = format(endDate, 'yyyy-MM-dd');
    }

    const response = await this.apiClient.get('/api/manager/tours/identifiers/distinct', {
      params: queryParams,
    });
    return response.data;
  }

  async getAllToursWithoutPagination(): Promise<ITourEntity[]> {
    let allTours: ITourEntity[] = [];
    let page = 1;
    const limit = 100;
    let hasMoreData = true;

    while (hasMoreData) {
      const response = await this.getTodayTours({ page, limit });
      allTours = [...allTours, ...response.items];
      hasMoreData = response.meta.totalPages > page;
      page++;
    }

    return allTours;
  }

  async getAllAssignmentsWithoutPagination(): Promise<ITourAssignmentEntity[]> {
    let allAssignments: ITourAssignmentEntity[] = [];
    let page = 1;
    const limit = 100;
    let hasMoreData = true;

    while (hasMoreData) {
      const response = await this.getTourAssignments({ page, limit });
      allAssignments = [...allAssignments, ...response.items];
      hasMoreData = response.meta.totalPages > page;
      page++;
    }

    return allAssignments;
  }
}

export const planningApiService = new PlanningApiService(apiClient);
