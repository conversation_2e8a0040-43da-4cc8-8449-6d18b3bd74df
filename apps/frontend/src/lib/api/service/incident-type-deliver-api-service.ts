import type { IIncidentTypeEntity } from '../../../interfaces/entity/i-incident-type-entity.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';

export class IncidentTypeDeliverApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async findAll(): Promise<IIncidentTypeEntity[]> {
    const response = await this.apiClient.get('/api/deliver/incident-types');
    return response.data;
  }
}

export const incidentTypeDeliverApiService = new IncidentTypeDeliverApiService(apiClient);
