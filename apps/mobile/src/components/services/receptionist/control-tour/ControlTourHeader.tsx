import { LogisticsEquipmentKind } from '../../../../interfaces/enum/logistics-equipment-kind.enum';
import { useAppSelector } from '../../../../utils/redux';

const StepTitleMap = {
  [LogisticsEquipmentKind.PACKAGE]: 'Détails des colis',
  [LogisticsEquipmentKind.ROLL]: 'Détails des rouleaux',
  [LogisticsEquipmentKind.PALLET]: 'Détails des palettes',
};

export function ControlTourHeader() {
  const selectedTour = useAppSelector((state) => state.receptionistReturnControl.selectedTour);
  const currentStep = useAppSelector((state) => state.receptionistReturnControl.currentStep);

  return (
    <div className="flex flex-col p-6  ">
      <h2 className="text-lg font-bold p-0">{StepTitleMap[currentStep]}</h2>

      <div className="flex flex-col gap-2 bg-gray-50 p-4 rounded-lg">
        <h3 className="text-2xl font-bold p-0 !m-0">
          Tournée {selectedTour?.tourIdentifier?.originalNumber || '--'}
        </h3>
        <p className="p-0">Terminée le $TODO</p>
        <p className="p-0">{selectedTour?.stops?.length} livraisons</p>
      </div>
    </div>
  );
}
