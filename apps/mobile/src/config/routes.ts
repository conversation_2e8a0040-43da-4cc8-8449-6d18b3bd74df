import {
  AlertTriangle,
  Car,
  HelpCircle,
  Home,
  Key,
  Mail,
  Settings,
  User,
  UserCog,
} from 'lucide-react';
import React, { createContext } from 'react';
import { UserRole } from '../interfaces/enum/user-role.enum';

export const TitleContext = createContext<string>('Ionic App');

export interface RouteProps {
  path: string;
  label: string;
  icon?: React.ElementType;
  exact?: boolean;
  protected?: boolean;
  children?: RouteProps[];
  roles?: UserRole[];
  showInMainMenu?: boolean;
  showInTabMenu?: boolean;
}

export const PATHS = {
  // Routes publiques
  LOGIN: '/login',
  REGISTER: '/register',
  FORGOT_PASSWORD: '/forgot-password',
  RESET_PASSWORD: '/reset-password/:token',

  // Routes protégées
  ADMIN: '/admin',
  ROLE_SELECTION: '/admin/role-selection',
  DASHBOARD: '/admin/dashboard',
  DASHBOARD_DELIVERER: '/admin/dashboard/deliverer',
  DASHBOARD_RECEPTIONIST: '/admin/dashboard/receptionist',
  // Espace Livreur
  LOADING_CONTROL: '/admin/deliverer/loading-control',
  TOURS: '/admin/deliverer/tours',
  TOUR_DETAIL: '/admin/deliverer/tours/:id',
  STOP_COMPLETION: '/admin/deliverer/stop/:id/complete',
  STOP_DELIVERY: '/admin/deliverer/stop/:stopId/delivery',
  INCIDENT: '/admin/deliverer/incident',

  // Espace Réceptionniste
  RECEPTIONIST_TOURS: '/admin/receptionist/tours',
  PRELOAD: '/admin/dashboard/receptionist/preload',
  RETURN_CONTROL_TOUR_LIST: '/admin/dashboard/receptionist/return-control',
  RETURN_CONTROL_TOUR: '/admin/dashboard/receptionist/return-control/:id',
  // Autres routes
  ACCOUNT: '/admin/account',
  SEARCH: '/admin/search',
  SETTINGS: '/admin/settings',
  HELP: '/admin/help',
};

export const routes: RouteProps[] = [
  {
    path: PATHS.DASHBOARD,
    label: 'Dashboard',
    icon: Home,
    exact: true,
    protected: true,
    showInMainMenu: true,
    showInTabMenu: true,
    roles: [UserRole.DELIVERER, UserRole.RECEPTIONIST],
  },
  {
    path: PATHS.LOGIN,
    label: 'Connexion',
    icon: Mail,
    exact: true,
    protected: false,
    showInMainMenu: false,
  },
  {
    path: PATHS.REGISTER,
    label: 'Register',
    icon: User,
    exact: true,
    protected: false,
    showInMainMenu: false,
  },
  {
    path: PATHS.FORGOT_PASSWORD,
    label: 'Forgot Password',
    icon: Key,
    exact: true,
    protected: false,
    showInMainMenu: false,
  },

  {
    path: PATHS.TOURS,
    label: 'Tournée',
    icon: Car,
    exact: true,
    protected: true,
    showInMainMenu: false,
    showInTabMenu: true,
    roles: [UserRole.DELIVERER],
  },
  {
    path: PATHS.RECEPTIONIST_TOURS,
    label: 'Tournées',
    icon: Car,
    exact: true,
    protected: true,
    showInMainMenu: false,
    showInTabMenu: false,
  },
  {
    path: PATHS.ACCOUNT,
    label: 'Compte',
    icon: UserCog,
    exact: true,
    protected: true,
    showInMainMenu: false,
    showInTabMenu: true,
    roles: [UserRole.DELIVERER, UserRole.RECEPTIONIST],
  },
  {
    path: PATHS.INCIDENT,
    label: 'Accident',
    icon: AlertTriangle,
    exact: true,
    protected: true,
    showInMainMenu: false,
    showInTabMenu: true,
    roles: [UserRole.DELIVERER],
  },

  {
    path: PATHS.SETTINGS,
    label: 'Settings',
    icon: Settings,
    exact: true,
    protected: true,
    showInMainMenu: true,
    showInTabMenu: false,
  },
  {
    path: PATHS.HELP,
    label: 'Help',
    icon: HelpCircle,
    exact: true,
    protected: true,
    showInMainMenu: false,
    showInTabMenu: false,
  },
];

// Fonctions de filtrage pour obtenir des sous-ensembles de routes
export const getPublicRoutes = () => routes.filter((route) => !route.protected);
export const getProtectedRoutes = () => routes.filter((route) => route.protected);
export const getMainMenuRoutes = () => routes.filter((route) => route.showInMainMenu);

export const getTabMenuRoutes = (userRoles: UserRole[] = []) => {
  const tabRoutes = routes.filter((route) => {
    if (!route.showInTabMenu) {
      return false;
    }

    if (route.roles && route.roles.length > 0) {
      return route.roles.some((role) => userRoles.includes(role));
    }

    return true;
  });

  return tabRoutes;
};

export const findRouteByPath = (path: string): RouteProps | undefined => {
  return routes.find((route) => route.path === path);
};

export const getRouteTitle = (path: string): string => {
  const route = findRouteByPath(path);

  return route ? route.label : 'IRNE Monorepo App';
};

export default {
  PATHS,
  routes,
  getPublicRoutes,
  getProtectedRoutes,
  getMainMenuRoutes,
  getTabMenuRoutes,
  findRouteByPath,
  getRouteTitle,
};
