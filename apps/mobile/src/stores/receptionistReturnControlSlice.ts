import { createSelector, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { LogisticsEquipmentKind } from '../interfaces/enum/logistics-equipment-kind.enum';
import { TourReceptionistService } from '../services/TourReceptionistService';
import { RootState } from './store';
import { getToursWithQueueProjection } from './tourSlice';

type LogisticEquipmentTypeId = string;

export const RECEPTIONIST_RETURN_CONTROL_STEPS = [
  LogisticsEquipmentKind.PACKAGE,
  LogisticsEquipmentKind.ROLL,
  LogisticsEquipmentKind.PALLET,
];

export interface ReceptionistReturnControlState {
  selectedTour: ITourEntity;
  equipmentValidationDetails: Record<LogisticEquipmentTypeId, number>;
  currentStep: LogisticsEquipmentKind;
}

const initialState: ReceptionistReturnControlState = {
  selectedTour: {} as ITourEntity,
  equipmentValidationDetails: {},
  currentStep: RECEPTIONIST_RETURN_CONTROL_STEPS[0],
};

export const receptionistReturnControlSlice = createSlice({
  name: 'receptionistReturnControl',
  initialState,
  reducers: {
    initStore: (state, action: PayloadAction<ITourEntity>) => {
      state.selectedTour = action.payload;
      state.currentStep = LogisticsEquipmentKind.PACKAGE;
      state.equipmentValidationDetails = {};
    },
    setEquipmentValidationQuantity: (
      state,
      action: PayloadAction<{ equipmentTypeId: string; quantity: number }>,
    ) => {
      state.equipmentValidationDetails[action.payload.equipmentTypeId] = action.payload.quantity;
    },
    setCurrentStep: (state, action: PayloadAction<LogisticsEquipmentKind>) => {
      state.currentStep = action.payload;
    },
    nextStep: (state) => {
      const currentStepIndex = RECEPTIONIST_RETURN_CONTROL_STEPS.indexOf(state.currentStep);
      state.currentStep = RECEPTIONIST_RETURN_CONTROL_STEPS[currentStepIndex + 1];
    },
  },
});

export const receptionistReturnControlActions = {
  initStore: receptionistReturnControlSlice.actions.initStore,
  setEquipmentValidationQuantity:
    receptionistReturnControlSlice.actions.setEquipmentValidationQuantity,
  setCurrentStep: receptionistReturnControlSlice.actions.setCurrentStep,
  nextStep: receptionistReturnControlSlice.actions.nextStep,
};

export const receptionistReturnControlSelectors = {
  toursEligibleForReturnControl: createSelector(getToursWithQueueProjection, (tours) =>
    tours.filter((tour) => TourReceptionistService.isTourEligibleForReturnControl(tour)),
  ),
  logisticEquipmentsTypesForCurrentStep: createSelector(
    (state: RootState) => state.receptionistReturnControl.currentStep,
    (state: RootState) => state.logisticEquipments.equipmentTypes,
    (currentStep, equipmentTypes) => {
      return equipmentTypes.filter((equipmentType) => equipmentType.kind === currentStep);
    },
  ),
};

export default receptionistReturnControlSlice.reducer;
