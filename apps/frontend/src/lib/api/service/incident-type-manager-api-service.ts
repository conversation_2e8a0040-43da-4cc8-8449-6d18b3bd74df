import type { IIncidentTypeEntity } from '../../../interfaces/entity/i-incident-type-entity.ts';
import type { IPaginatedResponse, IPaginationParams } from '../../../interfaces/pagination.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';
import type {
  ICreateIncidentTypeDto,
  IUpdateIncidentTypeDto,
} from '../../dto/incident-type.dto.ts';

export class IncidentTypeManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async create(data: ICreateIncidentTypeDto): Promise<IIncidentTypeEntity> {
    const response = await this.apiClient.post('/api/manager/incident-types', data);
    return response.data;
  }

  async findAll(params?: IPaginationParams): Promise<IPaginatedResponse<IIncidentTypeEntity>> {
    const response = await this.apiClient.get('/api/manager/incident-types', { params });
    return response.data;
  }

  async findOne(id: string): Promise<IIncidentTypeEntity> {
    const response = await this.apiClient.get(`/api/manager/incident-types/${id}`);
    return response.data;
  }

  async update(id: string, data: IUpdateIncidentTypeDto): Promise<IIncidentTypeEntity> {
    const response = await this.apiClient.patch(`/api/manager/incident-types/${id}`, data);
    return response.data;
  }

  async delete(id: string): Promise<void> {
    await this.apiClient.delete(`/api/manager/incident-types/${id}`);
  }
}

export const incidentTypeManagerApiService = new IncidentTypeManagerApiService(apiClient);
