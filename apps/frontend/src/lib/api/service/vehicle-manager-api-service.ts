import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';
import type { IPaginatedResponse, IPaginationParams } from '../../../interfaces/pagination.ts';
import type { IVehicleEntity } from '../../../interfaces/entity/i-vehicle-entity.ts';
import type { ICreateVehicleDto, IUpdateVehicleDto } from '../../dto/vehicle.dto.ts';

export class VehicleManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async create(data: ICreateVehicleDto): Promise<IVehicleEntity> {
    const response = await this.apiClient.post('/api/manager/vehicles', data);
    return response.data;
  }

  async findAll(params?: IPaginationParams): Promise<IPaginatedResponse<IVehicleEntity>> {
    const response = await this.apiClient.get('/api/manager/vehicles', { params });
    return response.data;
  }

  async findOne(id: string): Promise<IVehicleEntity> {
    const response = await this.apiClient.get(`/api/manager/vehicles/${id}`);
    return response.data;
  }

  async update(id: string, data: IUpdateVehicleDto): Promise<IVehicleEntity> {
    const response = await this.apiClient.patch(`/api/manager/vehicles/${id}`, data);
    return response.data;
  }

  async remove(id: string): Promise<void> {
    await this.apiClient.delete(`/api/manager/vehicles/${id}`);
  }
}

export const vehicleManagerApiService = new VehicleManagerApiService(apiClient);
