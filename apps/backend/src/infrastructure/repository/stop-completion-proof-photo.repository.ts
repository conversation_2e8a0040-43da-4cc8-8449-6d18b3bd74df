import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { StopCompletionProofPhotoEntity } from '../../domain/entity/stop-completion-proof-photo.entity';

@Injectable()
export class StopCompletionProofPhotoRepository extends Repository<StopCompletionProofPhotoEntity> {
  constructor(
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(StopCompletionProofPhotoEntity, entityManager || dataSource.createEntityManager());
  }
}