import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import type { IUserEntity } from '../../interfaces/entity/i-user-entity';
import { userApiService } from '@/lib/api/service/user-api-service';
import { keycloakService } from '../services/keycloak.service';

export interface CurrentUserState {
  currentUser: IUserEntity | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  isInitialized: boolean;
  error: string | null;
}

const initialState: CurrentUserState = {
  currentUser: null,
  isLoading: true,
  isAuthenticated: false,
  isInitialized: false,
  error: null,
};

export const initAuth = createAsyncThunk<IUserEntity | null, void>(
  'currentUser/initAuth',
  async (_, { rejectWithValue }) => {
    const isAuthenticated = keycloakService.keycloak.authenticated;

    if (!isAuthenticated) {
      return null;
    }
    try {
      const currentUser = await userApiService.getCurrentUser();

      return currentUser;
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }

      return rejectWithValue('An unknown error occurred');
    }
  },
);

export const signOut = createAsyncThunk<void, void>(
  'currentUser/signOut',
  async (_, { rejectWithValue }) => {
    try {
      await keycloakService.logout();
    } catch (error: unknown) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  },
);

const currentUserSlice = createSlice({
  name: 'currentUser',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(initAuth.pending, (state) => {
      state.isLoading = true;
    });
    builder.addCase(initAuth.fulfilled, (state, action) => {
      state.isLoading = false;
      state.isAuthenticated = action.payload !== null;
      state.currentUser = action.payload;
      state.isInitialized = true;
    });
    builder.addCase(initAuth.rejected, (state, action) => {
      state.isLoading = false;
      state.error = action.payload as string;
      state.isInitialized = true;
    });
    builder.addCase(signOut.fulfilled, (state) => {
      state.isAuthenticated = false;
      state.currentUser = null;
    });
  },
});

export const {} = currentUserSlice.actions;

export default currentUserSlice.reducer;
