import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { getUserColor as getColorFromUtils } from '@/lib/color-utils.ts';

/**
 * Get display name for a user entity
 */
export const getUserDisplayName = (user: IUserEntity): string => {
  if (user.firstName && user.lastName) {
    return `${user.firstName} ${user.lastName}`;
  }
  return user.username;
};

/**
 * Get avatar initials for a user entity
 */
export const getUserAvatar = (user: IUserEntity): string => {
  if (user.firstName && user.lastName) {
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
  }
  return user.username.charAt(0).toUpperCase();
};

/**
 * Get color for a user entity
 */
export const getUserColor = (user: IUserEntity): string => {
  return getColorFromUtils(user.color, user.id);
};

/**
 * Calculate consistent cell height for planning components
 * Based on the number of distinct users (slots) assigned to a tour
 */
export const calculateCellHeight = (totalSlots: number): number => {
  return Math.max(64, totalSlots * 32 + 16);
};
