'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { importManagerApiService } from '@/lib/api/service/import-manager-api-service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AlertCircleIcon, CheckCircleIcon, LoaderIcon, PlayIcon } from 'lucide-react';
import { toast } from 'sonner';
import { AxiosError } from 'axios';

interface TriggerImportDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function TriggerImportDialog({ open, onOpenChange }: TriggerImportDialogProps) {
  const queryClient = useQueryClient();

  const { data: importStatus } = useQuery({
    queryKey: ['sftp-import-status'],
    queryFn: () => importManagerApiService.getSftpImportStatus(),
    refetchInterval: 2000,
    enabled: open,
  });

  const isImportInProgress = importStatus?.isImportInProgress ?? false;

  const triggerImportMutation = useMutation({
    mutationFn: () =>
      importManagerApiService.triggerSftpImport(new Date().toISOString().split('T')[0]),
    onSuccess: (data) => {
      toast.success('Import déclenché avec succès', {
        description: "L'import SFTP a été lancé et sera traité en arrière-plan.",
        icon: <CheckCircleIcon className="h-4 w-4" />,
      });
      queryClient.invalidateQueries({ queryKey: ['imports'] });
      onOpenChange(false);
    },
    onError: (error: AxiosError) => {
      if (error.response?.status === 409) {
        toast.error('Import déjà en cours', {
          description:
            "Un import SFTP est déjà en cours d'exécution. Veuillez attendre qu'il se termine avant d'en lancer un nouveau.",
          icon: <AlertCircleIcon className="h-4 w-4" />,
        });
      } else {
        toast.error("Erreur lors du déclenchement de l'import", {
          description: error?.message || "Une erreur inattendue s'est produite.",
          icon: <AlertCircleIcon className="h-4 w-4" />,
        });
      }
    },
  });

  const handleTriggerImport = () => {
    triggerImportMutation.mutate();
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <PlayIcon className="h-5 w-5 text-primary" />
            Déclencher un nouvel import
          </DialogTitle>
          <DialogDescription>
            Cette action va déclencher un import SFTP pour récupérer et traiter les nouveaux
            fichiers de tournées.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {isImportInProgress ? (
            <div className="rounded-lg border border-orange-200 bg-orange-50 p-3">
              <div className="flex items-start gap-2">
                <LoaderIcon className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0 animate-spin" />
                <div className="text-sm text-orange-800">
                  <p className="font-medium">Import en cours :</p>
                  <p>
                    Un import SFTP est actuellement en cours d'exécution. Veuillez attendre qu'il se
                    termine avant d'en lancer un nouveau.
                  </p>
                </div>
              </div>
            </div>
          ) : (
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3">
              <div className="flex items-start gap-2">
                <AlertCircleIcon className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800">
                  <p className="font-medium">Information importante :</p>
                  <p>
                    L'import va traiter tous les nouveaux fichiers disponibles sur le serveur SFTP.
                    Cette opération peut prendre plusieurs minutes selon le nombre de fichiers.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={triggerImportMutation.isPending}
          >
            Annuler
          </Button>
          <Button
            onClick={handleTriggerImport}
            disabled={triggerImportMutation.isPending || isImportInProgress}
            className="flex items-center gap-2"
          >
            {triggerImportMutation.isPending ? (
              <LoaderIcon className="h-4 w-4 animate-spin" />
            ) : (
              <PlayIcon className="h-4 w-4" />
            )}
            {triggerImportMutation.isPending ? 'Déclenchement...' : "Déclencher l'import"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

export default TriggerImportDialog;
