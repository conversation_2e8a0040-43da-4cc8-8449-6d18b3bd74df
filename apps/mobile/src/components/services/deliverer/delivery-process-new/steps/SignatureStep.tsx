import { IonButton, IonIcon, IonInput, IonItem, IonModal, IonText } from '@ionic/react';
import { pencil, save } from 'ionicons/icons';
import { useMemo, useState } from 'react';
import { Base64 } from '../../../../../services/FileSystemService';
import { deliveryProcessActions } from '../../../../../stores/deliveryProcessSlice';
import { useAppDispatch, useAppSelector } from '../../../../../utils/redux';
import SignatureCapture from '../../../../ui/SignatureCapture';
import { StopDeliveryFooter } from '../StopDeliveryFooter';

export function SignatureStep() {
  const dispatch = useAppDispatch();
  const stopCompletionFormData = useAppSelector(
    (state) => state.deliveryProcess.stopCompletionFormData,
  );
  const stop = useAppSelector((state) => state.deliveryProcess.stop);
  const [showSignatureModal, setShowSignatureModal] = useState(false);

  const email = useMemo(() => {
    return stopCompletionFormData?.signatureEmail || stop.originalClientInfo?.email;
  }, [stopCompletionFormData?.signatureEmail, stop.originalClientInfo?.email]);

  const firstName = useMemo(() => {
    return stopCompletionFormData?.signatureFirstName;
  }, [stopCompletionFormData?.signatureFirstName]);

  const lastName = useMemo(() => {
    return stopCompletionFormData?.signatureLastName;
  }, [stopCompletionFormData?.signatureLastName]);

  const signatureBase64 = useMemo(() => {
    return stopCompletionFormData?.signatureFile?.base64;
  }, [stopCompletionFormData]);

  const isFormValid = useMemo(() => {
    if (stopCompletionFormData.incidentTypeId) {
      return true;
    }

    if (stopCompletionFormData.isSecureLocation) {
      return true;
    }

    return !!signatureBase64 && !!email;
  }, [signatureBase64, stopCompletionFormData.isSecureLocation, email]);

  const handleSignatureCapture = (signature: Base64) => {
    dispatch(
      deliveryProcessActions.setStopCompletionFormData({
        signatureFile: { base64: signature, filename: 'signature.png', mimeType: 'image/png' },
      }),
    );
    setShowSignatureModal(false);
  };

  const handleEmailChange = (email: string) => {
    const parsedEmail = email.trim().toLowerCase();

    if (parsedEmail.length > 0) {
      dispatch(deliveryProcessActions.setStopCompletionFormData({ signatureEmail: parsedEmail }));
    } else {
      dispatch(deliveryProcessActions.setStopCompletionFormData({ signatureEmail: undefined }));
    }
  };

  const handleFirstNameChange = (firstName: string) => {
    const parsedFirstName = firstName.trim();

    if (parsedFirstName.length > 0) {
      dispatch(
        deliveryProcessActions.setStopCompletionFormData({ signatureFirstName: parsedFirstName }),
      );
    } else {
      dispatch(deliveryProcessActions.setStopCompletionFormData({ signatureFirstName: undefined }));
    }
  };

  const handleLastNameChange = (lastName: string) => {
    const parsedLastName = lastName.trim();

    if (parsedLastName.length > 0) {
      dispatch(
        deliveryProcessActions.setStopCompletionFormData({ signatureLastName: parsedLastName }),
      );
    } else {
      dispatch(deliveryProcessActions.setStopCompletionFormData({ signatureLastName: undefined }));
    }
  };

  const handleNextStep = async () => {
    dispatch(deliveryProcessActions.setNextStep());
  };

  return (
    <>
      <div className="pt-6 pb-6 ">
        <div className="mb-6 sm:mb-8">
          <div className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold flex items-center gap-2 sm:gap-3 mb-4 sm:mb-6">
            Signature du destinataire
          </div>

          {signatureBase64 && (
            <img src={signatureBase64} alt="Signature" className="w-48 h-24 object-contain" />
          )}

          <IonButton
            expand="block"
            fill="outline"
            color={signatureBase64 ? 'success' : 'primary'}
            onClick={() => setShowSignatureModal(true)}
            className="h-12 sm:h-14 md:h-16 rounded-lg"
          >
            <div className="flex items-center gap-2 justify-center">
              <IonIcon icon={signatureBase64 ? save : pencil} />
              <span className="text-sm sm:text-base md:text-lg font-semibold">
                {signatureBase64 ? 'SIGNATURE ENREGISTRÉE' : 'SIGNER'}
              </span>
            </div>
          </IonButton>
        </div>

        <div className="mb-8 sm:mb-10 md:mb-12">
          <div className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-4 sm:mb-6">
            Informations client
          </div>

          <div className="space-y-4 sm:space-y-5 md:space-y-6">
            <div>
              <IonText className="text-xs sm:text-sm md:text-base font-medium text-gray-700 block mb-2">
                EMAIL CLIENT
              </IonText>
              <IonItem className="border border-gray-300 rounded-lg h-12 sm:h-14 md:h-16">
                <IonInput
                  type="email"
                  value={email}
                  placeholder="<EMAIL>"
                  className="text-sm sm:text-base md:text-lg"
                  onIonInput={(e) => handleEmailChange(e.detail.value || '')}
                />
              </IonItem>
            </div>

            <div>
              <IonText className="text-xs sm:text-sm md:text-base font-medium text-gray-700 block mb-2">
                PRÉNOM
              </IonText>
              <IonItem className="border border-gray-300 rounded-lg h-12 sm:h-14 md:h-16">
                <IonInput
                  type="text"
                  value={firstName}
                  placeholder="Prénom du destinataire"
                  className="text-sm sm:text-base md:text-lg"
                  onIonInput={(e) => handleFirstNameChange(e.detail.value || '')}
                />
              </IonItem>
            </div>

            <div>
              <IonText className="text-xs sm:text-sm md:text-base font-medium text-gray-700 block mb-2">
                NOM
              </IonText>
              <IonItem className="border border-gray-300 rounded-lg h-12 sm:h-14 md:h-16">
                <IonInput
                  type="text"
                  value={lastName}
                  placeholder="Nom du destinataire"
                  className="text-sm sm:text-base md:text-lg"
                  onIonInput={(e) => handleLastNameChange(e.detail.value || '')}
                />
              </IonItem>
            </div>
          </div>
        </div>

        <IonModal isOpen={showSignatureModal} onDidDismiss={() => setShowSignatureModal(false)}>
          <SignatureCapture
            onSignatureCapture={handleSignatureCapture}
            onCancel={() => setShowSignatureModal(false)}
          />
        </IonModal>
      </div>

      <StopDeliveryFooter isDisabled={!isFormValid} onClick={handleNextStep} />
    </>
  );
}
