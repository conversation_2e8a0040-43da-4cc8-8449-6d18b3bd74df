'use client';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { LogisticsEquipmentTypeForm } from '../logistics-equipment-type-form';
import { logisticsEquipmentTypeManagerApiService } from '@/lib/api/service/logistics-equipment-type-manager-api-service';
import type { ICreateLogisticsEquipmentTypeDto } from '@/lib/dto/logistics-equipment-type.dto';

interface CreateLogisticsEquipmentTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateLogisticsEquipmentTypeDialog({
  open,
  onOpenChange,
}: CreateLogisticsEquipmentTypeDialogProps) {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: (data: ICreateLogisticsEquipmentTypeDto) =>
      logisticsEquipmentTypeManagerApiService.create(data),
    onSuccess: () => {
      toast.success("Type d'équipement créé avec succès");
      queryClient.invalidateQueries({ queryKey: ['logistics-equipment-types'] });
      onOpenChange(false);
    },
    onError: (error: unknown) => {
      const message =
        error &&
        typeof error === 'object' &&
        'response' in error &&
        error.response &&
        typeof error.response === 'object' &&
        'data' in error.response &&
        error.response.data &&
        typeof error.response.data === 'object' &&
        'message' in error.response.data
          ? String(error.response.data.message)
          : 'Une erreur est survenue';
      toast.error(message);
    },
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Créer un type d'équipement</DialogTitle>
          <DialogDescription>
            Remplissez les informations pour créer un nouveau type d'équipement logistique.
          </DialogDescription>
        </DialogHeader>

        <LogisticsEquipmentTypeForm
          onSubmit={(data) => createMutation.mutate(data)}
          isLoading={createMutation.isPending}
          onCancel={() => onOpenChange(false)}
        />
      </DialogContent>
    </Dialog>
  );
}
