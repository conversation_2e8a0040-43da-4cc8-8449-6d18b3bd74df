import type { ILogisticsEquipmentTypeEntity } from '../../../interfaces/entity/i-logistics-equipment-type-entity.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';

export class LogisticsEquipmentTypeDeliverApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async findAll(): Promise<ILogisticsEquipmentTypeEntity[]> {
    const response = await this.apiClient.get('/api/deliver/logistics-equipment-types');
    return response.data;
  }

  async findOne(id: string): Promise<ILogisticsEquipmentTypeEntity> {
    const response = await this.apiClient.get(`/api/deliver/logistics-equipment-types/${id}`);
    return response.data;
  }
}

export const logisticsEquipmentTypeDeliverApiService = new LogisticsEquipmentTypeDeliverApiService(
  apiClient,
);
