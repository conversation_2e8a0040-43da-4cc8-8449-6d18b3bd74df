import { useEffect } from 'react';
import { ITourEntity } from '../../../../interfaces/entity/i-tour-entity';
import { receptionistReturnControlActions } from '../../../../stores/receptionistReturnControlSlice';
import { useAppDispatch, useAppSelector } from '../../../../utils/redux';
import { ControlTourFooter } from './ControlTourFooter';
import { ControlTourHeader } from './ControlTourHeader';
import { EquipmentDetailsList } from './EquipmentDetailsList';

export function ReceptionistControlTour({ tour }: { tour: ITourEntity }) {
  const selectedTour = useAppSelector((state) => state.receptionistReturnControl.selectedTour);
  const dispatch = useAppDispatch();

  //TODO A restyliser

  useEffect(() => {
    if (selectedTour.id !== tour.id) {
      dispatch(receptionistReturnControlActions.initStore(tour));
    }
  }, [tour, selectedTour.id, dispatch]);

  return (
    <div>
      <ControlTourHeader />
      <div>
        <EquipmentDetailsList />
      </div>
      <div>
        <ControlTourFooter />
      </div>
    </div>
  );
}
