import { Injectable } from '@nestjs/common';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { TourIdentifier } from '../../domain/entity/tour-identifier';
import { TourEntity } from '../../domain/entity/tour.entity';
import { TourAssignmentRepository } from './tour-assignment.repository'; // Added import

@Injectable()
export class TourRepository extends Repository<TourEntity> {
  constructor(
    private readonly tourAssignmentRepository: TourAssignmentRepository, // Injected TourAssignmentRepository
    private entityManager?: EntityManager,
    private dataSource?: DataSource,
  ) {
    if (!dataSource && !entityManager) {
      throw new Error('Cannot instantiate repository without dataSource or entityManager');
    }

    super(TourEntity, entityManager || dataSource.createEntityManager());
  }

  public async findByTourIdentifierForDate(tourIdentifier: TourIdentifier, date: string): Promise<TourEntity> {
    const tour = await this.findOne({
      where: {
        tourIdentifier: tourIdentifier,
        deliveryDate: date,
      },
    });

    return tour;
  }

  public async findAssignedToursByUserAndDate(userId: string, date: string): Promise<TourEntity[]> {
    const assignments = await this.tourAssignmentRepository.findAssignmentsByUserAndDate(userId, date);

    if (!assignments || assignments.length === 0) {
      return [];
    }

    const tours: TourEntity[] = [];

    for (const assignment of assignments) {
      // The 'date' parameter for findAssignedToursByUserAndDate corresponds to 'deliveryDate' in TourEntity
      // and is the same 'date' used to fetch assignments.
      const tour = await this.findByTourIdentifierForDate(assignment.tourIdentifier, date);

      if (tour) {
        tours.push(tour);
      }
    }

    return tours;
  }

  public async findDistinctOriginalTourNumbers(): Promise<string[]> {
    const result = await this.createQueryBuilder('tour')
      .select('DISTINCT tour.tourIdentifier.originalNumber', 'originalNumber')
      .orderBy('tour.tourIdentifier.originalNumber', 'ASC')
      .getRawMany();

    return result.map((row) => row.originalNumber);
  }

  public async findDistinctTourIdentifiers(startDate?: string, endDate?: string): Promise<TourIdentifier[]> {
    const queryBuilder = this.createQueryBuilder('tour').select([
      'DISTINCT tour.tourIdentifier.originalNumber as original_number',
      'tour.tourIdentifier.number as number',
      'tour.tourIdentifier.type as type',
    ]);

    if (startDate && endDate) {
      queryBuilder.where('tour.deliveryDate BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    } else if (startDate) {
      queryBuilder.where('tour.deliveryDate >= :startDate', { startDate });
    } else if (endDate) {
      queryBuilder.where('tour.deliveryDate <= :endDate', { endDate });
    }

    queryBuilder.orderBy('tour.tourIdentifier.originalNumber', 'ASC');

    const rawResults = await queryBuilder.getRawMany();

    return rawResults.map((row) => {
      const tourIdentifier = new TourIdentifier();

      tourIdentifier.originalNumber = row.original_number;
      tourIdentifier.number = row.number;
      tourIdentifier.type = row.type;

      return tourIdentifier;
    });
  }

  public async updateTourStatus(tourId: string): Promise<void> {
    await this.query(
      `
      UPDATE tour
      SET status = (
        CASE
          WHEN EXISTS (
            SELECT 1 
            FROM stop 
            WHERE stop.tour_id = tour.id 
              AND stop.delivery_status = 'PENDING'
          )
          THEN 'IN_PROGRESS'::tour_status_enum
          ELSE 'COMPLETED'::tour_status_enum
        END
      )
      WHERE id = '${tourId}'
        AND status = 'IN_PROGRESS'::tour_status_enum;
    `,
    );
  }

  public async updateTourEquipmentCountsFromStops(tourId: string): Promise<void> {
    await this.query(
      `
      WITH equipment_totals AS (
        SELECT 
          tour_id,
          COALESCE(SUM(loaded_pallet_count), 0) as loaded_pallet_count,
          COALESCE(SUM(loaded_roll_count), 0) as loaded_roll_count,
          COALESCE(SUM(loaded_package_count), 0) as loaded_package_count,
          COALESCE(SUM(preloaded_pallet_count), 0) as preloaded_pallet_count,
          COALESCE(SUM(preloaded_roll_count), 0) as preloaded_roll_count,
          COALESCE(SUM(preloaded_package_count), 0) as preloaded_package_count,
          COALESCE(SUM(returned_pallet_count), 0) as returned_pallet_count,
          COALESCE(SUM(returned_roll_count), 0) as returned_roll_count,
          COALESCE(SUM(returned_package_count), 0) as returned_package_count,
          COALESCE(SUM(unloaded_pallet_count), 0) as unloaded_pallet_count,
          COALESCE(SUM(unloaded_roll_count), 0) as unloaded_roll_count,
          COALESCE(SUM(unloaded_package_count), 0) as unloaded_package_count
        FROM stop
        WHERE tour_id = '${tourId}'
        GROUP BY tour_id
      )
      UPDATE tour
      SET 
        total_loaded_pallet_count = et.loaded_pallet_count,
        total_loaded_roll_count = et.loaded_roll_count,
        total_loaded_package_count = et.loaded_package_count,
        total_preloaded_pallet_count = et.preloaded_pallet_count,
        total_preloaded_roll_count = et.preloaded_roll_count,
        total_preloaded_package_count = et.preloaded_package_count,
        total_returned_pallet_count = et.returned_pallet_count,
        total_returned_roll_count = et.returned_roll_count,
        total_returned_package_count = et.returned_package_count,
        total_unloaded_pallet_count = et.unloaded_pallet_count,
        total_unloaded_roll_count = et.unloaded_roll_count,
        total_unloaded_package_count = et.unloaded_package_count
      FROM equipment_totals et
      WHERE tour.id = '${tourId}'
        AND tour.id = et.tour_id;
    `,
    );
  }
}
