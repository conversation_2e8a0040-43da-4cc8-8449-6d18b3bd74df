import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';
import type { IPaginatedResponse, IPaginationParams } from '../../../interfaces/pagination.ts';
import type { IVehicleEntity } from '../../../interfaces/entity/i-vehicle-entity.ts';

export class VehicleDeliverApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async findAll(params?: IPaginationParams): Promise<IPaginatedResponse<IVehicleEntity>> {
    const response = await this.apiClient.get('/api/deliver/vehicles', { params });
    return response.data;
  }

  async findOne(id: string): Promise<IVehicleEntity> {
    const response = await this.apiClient.get(`/api/deliver/vehicles/${id}`);
    return response.data;
  }
}

export const vehicleDeliverApiService = new VehicleDeliverApiService(apiClient);
