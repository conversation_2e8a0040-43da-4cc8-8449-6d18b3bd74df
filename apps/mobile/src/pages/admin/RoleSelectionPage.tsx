import { IonContent, Ion<PERSON><PERSON>er, IonPage, IonTitle, IonToolbar } from '@ionic/react';
import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import Button from '../../components/ui/Button';
import { PATHS } from '../../config/routes';
import { UserRole } from '../../interfaces/enum/user-role.enum';
import { setActiveRole } from '../../stores/currentUserSlice';
import { useAppDispatch, useAppSelector } from '../../utils/redux';

const RoleSelectionPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const history = useHistory();
  const { user } = useAppSelector((state) => state.currentUser);
  const userRoles = user?.roles || [];
  const isDevMode = import.meta.env.VITE_ENV_MODE === 'dev';

  const availableMobileRoles = userRoles.filter(
    (role) => role === UserRole.DELIVERER || role === UserRole.RECEPTIONIST,
  );

  useEffect(() => {
    // En mode dev, on affiche la page de sélection si plus d'un rôle.
    // En prod, on prend le premier rôle dispo et on redirige.
    if (isDevMode && availableMobileRoles.length > 1) {
      return; // On affiche la page de sélection
    }

    if (availableMobileRoles.length === 1) {
      const roleToApply = availableMobileRoles[0];
      dispatch(setActiveRole(roleToApply));

      switch (roleToApply) {
        case UserRole.DELIVERER:
          history.replace(PATHS.DASHBOARD_DELIVERER);
          break;
        case UserRole.RECEPTIONIST:
          history.replace(PATHS.DASHBOARD);
          break;
      }
    }
  }, [availableMobileRoles, dispatch, history, isDevMode]);

  const handleRoleSelection = (role: UserRole) => {
    dispatch(setActiveRole(role));

    switch (role) {
      case UserRole.DELIVERER:
        history.replace(PATHS.DASHBOARD_DELIVERER);
        break;
      case UserRole.RECEPTIONIST:
        history.replace(PATHS.DASHBOARD);
        break;
    }
  };

  const getRoleLabel = (role: UserRole) => {
    switch (role) {
      case UserRole.DELIVERER:
        return 'Chauffeur';
      case UserRole.RECEPTIONIST:
        return "Gestionnaire d'agrés";
    }
  };

  return (
    <IonPage>
      <IonHeader>
        <IonToolbar>
          <IonTitle>Sélectionner un rôle</IonTitle>
        </IonToolbar>
      </IonHeader>
      <IonContent fullscreen className="ion-padding">
        <div className="flex flex-col items-center justify-center h-full gap-4">
          <h2 className="text-xl font-bold">Choisissez votre espace de travail</h2>
          {availableMobileRoles.map((role) => (
            <Button key={role} onClick={() => handleRoleSelection(role)}>
              {getRoleLabel(role)}
            </Button>
          ))}
        </div>
      </IonContent>
    </IonPage>
  );
};

export default RoleSelectionPage;
