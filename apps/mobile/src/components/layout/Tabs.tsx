import { <PERSON><PERSON>abel, IonRouterOutlet, IonTabBar, IonTabButton, IonTabs } from '@ionic/react';
import { Redirect, Route } from 'react-router-dom';
import { getTabMenuRoutes, PATHS } from '../../config/routes';
import { DeliveryProcessPage } from '../../pages/admin/DeliveryProcess';
import RoleSelectionPage from '../../pages/admin/RoleSelectionPage';

import Profile from '../../pages/admin/account/view';
import DelivererDashboard from '../../pages/admin/dashboard/deliverer/DelivererDashboard';
import { DelivererLoadingControlPage } from '../../pages/admin/dashboard/deliverer/LoadingControl';
import { ControlTourPage } from '../../pages/admin/dashboard/receptionist/ControlTourPage';
import PreloadPage from '../../pages/admin/dashboard/receptionist/PreloadPage';
import { ReceptionistControlTourListPage } from '../../pages/admin/dashboard/receptionist/ReceptionistControlTourListPage';
import ReceptionistDashboard from '../../pages/admin/dashboard/receptionist/ReceptionistDashboard';
import { DashboardView } from '../../pages/admin/dashboard/view';
import IncidentView from '../../pages/admin/incident/view';
import { useAppSelector } from '../../utils/redux';
import TourDeliveryStopList from '../services/deliverer/TourDeleveryStopList';

const Tabs: React.FC = () => {
  const { activeRole } = useAppSelector((state) => state.currentUser);
  const tabMenuRoutes = getTabMenuRoutes(activeRole ? [activeRole] : []);

  const getTabId = (path: string): string => {
    return path.replace('/admin/', '').replace(/\//g, '-') || 'home';
  };

  return (
    <IonTabs>
      <IonRouterOutlet>
        {/* ATTENTION CETTE PAGE NE S'ACTIVE QU'EN DEV */}
        <Route exact path={PATHS.ROLE_SELECTION} component={RoleSelectionPage} />

        {/* Routes pour les onglets */}
        <Route exact path={PATHS.LOADING_CONTROL} component={DelivererLoadingControlPage} />
        <Route exact path={PATHS.ACCOUNT} component={Profile} />
        <Route exact path={PATHS.INCIDENT} component={IncidentView} />
        <Route exact path={PATHS.DASHBOARD} component={DashboardView} />
        <Route exact path={PATHS.DASHBOARD_DELIVERER} component={DelivererDashboard} />
        <Route exact path={PATHS.DASHBOARD_RECEPTIONIST} component={ReceptionistDashboard} />
        <Route exact path={PATHS.TOURS} component={TourDeliveryStopList} />
        <Route exact path={PATHS.STOP_DELIVERY} component={DeliveryProcessPage} />
        <Route exact path={PATHS.PRELOAD} component={PreloadPage} />
        <Route
          exact
          path={PATHS.RETURN_CONTROL_TOUR_LIST}
          component={ReceptionistControlTourListPage}
        />
        <Route exact path={PATHS.RETURN_CONTROL_TOUR} component={ControlTourPage} />

        <Route exact path={PATHS.ADMIN}>
          <Redirect to={PATHS.ROLE_SELECTION} />
        </Route>
      </IonRouterOutlet>

      <IonTabBar slot="bottom" className="border-t py-2 primary-bg">
        {tabMenuRoutes.map((route, index) => {
          const tabId = getTabId(route.path);

          return (
            <IonTabButton key={index} tab={tabId} href={route.path} className="primary-button">
              <div className="flex flex-col items-center justify-center gap-1">
                {route.icon ? <route.icon className="text-white" size={20} /> : null}
                <IonLabel className="text-white text-xs">{route.label}</IonLabel>
              </div>
            </IonTabButton>
          );
        })}
      </IonTabBar>
    </IonTabs>
  );
};

export default Tabs;
