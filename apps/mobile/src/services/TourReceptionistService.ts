import { IControlTourEquipmentDto } from '../interfaces/dto/control-tour-equipment.dto';
import { ITourEntity } from '../interfaces/entity/i-tour-entity';
import { TourStatus } from '../interfaces/enum/tour.enums';
import { apiService } from './ApiService';

export class TourReceptionistService {
  private readonly endpoint = '/api/receptionist/tours';

  /**
   * Récupère tous les tours d'aujourd'hui pour le réceptionniste
   */
  async getTodayTours(): Promise<ITourEntity[]> {
    return apiService.get<ITourEntity[]>(`${this.endpoint}/today`);
  }

  /**
   * Contrôle l'équipement d'une tournée
   */
  async controlTourEquipment(tourId: string, dto: IControlTourEquipmentDto): Promise<ITourEntity> {
    return apiService.post<ITourEntity>(`${this.endpoint}/${tourId}/control-equipment`, dto);
  }

  static isTourControlled(tour: ITourEntity): boolean {
    return (
      tour.controlledEquipmentCount?.packageCount != null ||
      tour.controlledEquipmentCount?.palletCount != null ||
      tour.controlledEquipmentCount?.rollCount != null
    );
  }

  static isTourEligibleForReturnControl(tour: ITourEntity): boolean {
    if (tour.status !== TourStatus.Completed) {
      return false;
    }

    if (this.isTourControlled(tour)) {
      return false;
    }

    return true;
  }
}

export const tourReceptionistService = new TourReceptionistService();
