'use client';

import { <PERSON><PERSON>, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { IUserEntity } from '@/interfaces/entity/i-user-entity';
import { userApiService } from '@/lib/api/service/user-api-service';
import { ColumnDef } from '@tanstack/react-table';
import { EditIcon, KeyIcon, PlusIcon, TrashIcon } from 'lucide-react';
import { useState } from 'react';
import { UserRole } from '../../../../interfaces/enum/user-role.enum';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';
import { ExcelExportService } from '../../../../lib/services/excel-export.service';
import { CreateUserDialog } from './create-user-dialog';
import { EditUserDialog } from './edit-user-dialog';
import { ResetPasswordDialog } from '../reset-password-dialog';

interface UserTableSearchParams extends Record<string, unknown> {
  search?: string;
  role?: string;
}

export function UsersDataTable({ className }: UsersDataTableProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<IUserEntity | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [resetPasswordDialog, setResetPasswordDialog] = useState<{
    open: boolean;
    user: IUserEntity | null;
  }>({
    open: false,
    user: null,
  });

  const handleEditUser = (user: IUserEntity) => {
    setEditingUser(user);
    setIsEditDialogOpen(true);
  };

  const handleEditUsers = (selectedUsers: IUserEntity[]) => {
    console.log('Edit users:', selectedUsers);
  };

  const handleDeleteUsers = (selectedUsers: IUserEntity[]) => {
    console.log('Delete users:', selectedUsers);
  };

  const handleAddUser = () => {
    setIsCreateDialogOpen(true);
  };

  const handleResetPassword = (user: IUserEntity) => {
    setResetPasswordDialog({ open: true, user });
  };

  const columns: ColumnDef<IUserEntity>[] = [
    {
      id: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && 'indeterminate')
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Tout sélectionner"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Sélectionner la ligne"
        />
      ),
      size: 28,
      enableSorting: false,
      enableHiding: false,
    },
    {
      header: 'Utilisateur',
      accessorKey: 'username',
      cell: ({ row }) => (
        <div className="flex items-center gap-3">
          <Avatar className="h-8 w-8">
            <AvatarFallback
              className="text-xs font-medium"
              style={{ backgroundColor: row.original.color ?? undefined }}
            >
              {row.original.firstName?.[0] || row.original.username[0].toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div>
            <div className="flex items-center gap-2">
              <span className="font-medium">{row.original.username}</span>
              {row.original.isSSO && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  SSO
                </Badge>
              )}
            </div>
            {(row.original.firstName || row.original.lastName) && (
              <div className="text-sm text-muted-foreground">
                {[row.original.firstName, row.original.lastName].filter(Boolean).join(' ')}
              </div>
            )}
          </div>
        </div>
      ),
      size: 200,
      enableHiding: false,
    },
    {
      header: 'Email',
      accessorKey: 'email',
      cell: ({ row }) => (
        <div className="max-w-48 truncate">
          {row.original.email || <span className="text-muted-foreground italic">Aucun email</span>}
        </div>
      ),
      size: 200,
    },
    {
      header: 'Langue',
      accessorKey: 'locale',
      cell: ({ row }) => (
        <Badge variant="outline" className="uppercase">
          {row.original.locale || 'FR'}
        </Badge>
      ),
      size: 80,
      enableSorting: false,
    },
    {
      header: 'Couleur',
      accessorKey: 'color',
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <div
              className="w-4 h-4 rounded-full border border-gray-200"
              style={{ backgroundColor: row.original.color || '#cccccc' }}
              aria-label={`Couleur: ${row.original.color || 'Non définie'}`}
            />
            <span className="text-xs text-muted-foreground font-mono">
              {row.original.color || 'Non définie'}
            </span>
          </div>
        );
      },
      size: 120,
      enableSorting: true,
      enableHiding: true,
    },
    {
      header: 'Rôles',
      accessorKey: 'roles',
      cell: ({ row }) => {
        const roles = row.original.roles || [];
        const roleLabels = {
          'lrg-bl-manager': 'Manageur',
          'lrg-bl-receptionist': 'Réceptionniste',
          'lrg-bl-deliverer': 'Livreur',
        };

        return (
          <div className="flex flex-wrap gap-1">
            {roles.length > 0 ? (
              roles.map((role, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {roleLabels[role as keyof typeof roleLabels] || role}
                </Badge>
              ))
            ) : (
              <span className="text-muted-foreground italic text-xs">Aucun rôle</span>
            )}
          </div>
        );
      },
      size: 140,
      enableSorting: false,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEditUser(row.original)}
            className="h-8 w-8 p-0"
          >
            <EditIcon className="h-4 w-4" />
            <span className="sr-only">Modifier {row.original.username}</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleResetPassword(row.original)}
            className="h-8 w-8 p-0"
          >
            <KeyIcon className="h-4 w-4" />
            <span className="sr-only">
              Réinitialiser le mot de passe de {row.original.username}
            </span>
          </Button>
        </div>
      ),
      size: 120,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<IUserEntity, UserTableSearchParams> = {
    export: {
      enabled: true,
      fileName: 'utilisateurs',
      sheetName: 'Utilisateurs',
      excludeColumns: ['select', 'actions'],
      columnMap: {
        username: "Nom d'utilisateur",
        email: 'Email',
        locale: 'Langue',
        color: 'Couleur',
        roles: 'Rôles',
      },
      valueTransformers: {
        roles: (value) => {
          const roles = Array.isArray(value) ? value : [];
          const roleLabels = {
            'lrg-bl-manager': 'Manageur',
            'lrg-bl-receptionist': 'Réceptionniste',
            'lrg-bl-deliverer': 'Livreur',
          };
          return roles.length > 0
            ? roles.map((role) => roleLabels[role as keyof typeof roleLabels] || role).join(', ')
            : 'Aucun rôle';
        },
        locale: (value) => (value as string)?.toUpperCase() || 'FR',
        color: (value) => (value as string) || 'Non définie',
      },
    },
    columns,
    search: {
      enabled: true, // TODO: Implement server-side search in backend
      placeholder: "Rechercher des utilisateurs par nom d'utilisateur ou email...",
      searchKey: 'search',
    },
    filters: [
      {
        columnKey: 'roles',
        label: 'Rôle',
        type: 'select',
        options: [
          { label: 'Tous les rôles', value: 'all' },
          { label: 'Manageur', value: UserRole.Manager },
          { label: 'Réceptionniste', value: UserRole.Receptionist },
          { label: 'Livreur', value: UserRole.Deliverer },
          { label: 'Aucun rôle', value: 'null' },
        ],
        placeholder: 'Filtrer par rôle',
      },
    ],
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [
      {
        label: 'Modifier',
        icon: <EditIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleEditUsers,
        variant: 'outline',
        requiresSelection: true,
      },
      {
        label: 'Supprimer',
        icon: <TrashIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleDeleteUsers,
        variant: 'destructive',
        requiresSelection: true,
      },
      {
        label: 'Ajouter un utilisateur',
        icon: <PlusIcon className="-ms-1 opacity-60" size={16} aria-hidden="true" />,
        onClick: handleAddUser,
        variant: 'outline',
        requiresSelection: false,
      },
    ],
  };

  return (
    <>
      <PaginatedDataTable
        queryKey={['users', 'paginated']}
        queryFn={(params) => userApiService.getUsers(params)}
        config={config}
        className={className}
      />

      <CreateUserDialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen} />
      <EditUserDialog
        user={editingUser}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      />
      <ResetPasswordDialog
        user={resetPasswordDialog.user}
        open={resetPasswordDialog.open}
        onOpenChange={(open) => setResetPasswordDialog((prev) => ({ ...prev, open }))}
      />
    </>
  );
}

interface UsersDataTableProps {
  className?: string;
}

export default UsersDataTable;
