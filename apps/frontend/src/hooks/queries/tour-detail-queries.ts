import type { ITourEntity } from '@/interfaces/entity/i-tour-entity.ts';
import type { IStopEntity } from '@/interfaces/entity/i-stop-entity.ts';
import type { ITourIncidentDto } from '@/interfaces/entity/i-tour-incident-dto.ts';
import type { IPaginatedResponse, IPaginationParams } from '@/interfaces/pagination.ts';
import { tourManagerApiService } from '@/lib/api/service/tour-manager-api-service.ts';
import { keycloakService } from '@/lib/services/keycloak.service.ts';
import { useQuery } from '@tanstack/react-query';

export const tourDetailQueryKeys = {
  all: ['tour-detail'] as const,
  tour: (tourId: string) => [...tourDetailQueryKeys.all, 'tour', tourId] as const,
  stops: (tourId: string, params?: IPaginationParams) =>
    [...tourDetailQueryKeys.all, 'stops', tourId, params] as const,
  incidents: (tourId: string) => [...tourDetailQueryKeys.all, 'incidents', tourId] as const,
};

/**
 * Hook pour récupérer une tournée par son ID
 */
export const useTourByIdQuery = (tourId: string) => {
  return useQuery({
    queryKey: tourDetailQueryKeys.tour(tourId),
    queryFn: async (): Promise<ITourEntity> => {
      return tourManagerApiService.getTourById(tourId);
    },
    enabled: keycloakService.keycloak.authenticated && !!tourId,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
};

/**
 * Hook pour récupérer les stops d'une tournée avec pagination
 */
export const useTourStopsQuery = (tourId: string, params?: IPaginationParams) => {
  return useQuery({
    queryKey: tourDetailQueryKeys.stops(tourId, params),
    queryFn: async (): Promise<IPaginatedResponse<IStopEntity>> => {
      return tourManagerApiService.getTourStops(tourId, params);
    },
    enabled: keycloakService.keycloak.authenticated && !!tourId,
    staleTime: 1000 * 60 * 1, // 1 minute (plus fréquent car les stops changent souvent)
  });
};

/**
 * Hook pour récupérer les incidents des stops d'une tournée
 *
 * Note: Les incidents ne sont pas des entités standalone mais représentent
 * les incidents qui se sont produits sur les stops de cette tournée.
 */
export const useTourIncidentsQuery = (tourId: string) => {
  return useQuery({
    queryKey: tourDetailQueryKeys.incidents(tourId),
    queryFn: async (): Promise<ITourIncidentDto[]> => {
      return tourManagerApiService.getTourIncidents(tourId);
    },
    enabled: keycloakService.keycloak.authenticated && !!tourId,
    staleTime: 1000 * 60 * 2, // 2 minutes
  });
};
