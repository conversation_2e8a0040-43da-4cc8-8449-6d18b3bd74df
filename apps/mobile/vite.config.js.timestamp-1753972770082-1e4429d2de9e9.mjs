// vite.config.js
import tailwindcss from "file:///C:/Users/<USER>/Documents/DEV/lrg-bl/node_modules/.pnpm/@tailwindcss+vite@4.1.11_vi_317bcdc5f7cb36f88f6f38d9c2acd867/node_modules/@tailwindcss/vite/dist/index.mjs";
import legacy from "file:///C:/Users/<USER>/Documents/DEV/lrg-bl/node_modules/.pnpm/@vitejs+plugin-legacy@5.4.3_0361bf0fcb3cfe32f7064f6dba802728/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import react from "file:///C:/Users/<USER>/Documents/DEV/lrg-bl/node_modules/.pnpm/@vitejs+plugin-react@4.6.0__205ea5f87dfe9c6518413051b32e3de5/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { defineConfig } from "file:///C:/Users/<USER>/Documents/DEV/lrg-bl/node_modules/.pnpm/vite@5.2.14_@types+node@22._7377cf0b4f0e3be7c183a0706c2279ff/node_modules/vite/dist/node/index.js";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    tailwindcss(),
    legacy()
  ],
  server: {
    port: 8001,
    proxy: {
      "/api": {
        target: "http://localhost:3000",
        changeOrigin: true,
        rewrite: function(path) {
          return path.replace(/^\/api/, "");
        }
      }
    }
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,ewogICJ2ZXJzaW9uIjogMywKICAic291cmNlcyI6IFsidml0ZS5jb25maWcuanMiXSwKICAic291cmNlc0NvbnRlbnQiOiBbImNvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9kaXJuYW1lID0gXCJDOlxcXFxVc2Vyc1xcXFxhcnRkaVxcXFxEb2N1bWVudHNcXFxcREVWXFxcXGxyZy1ibFxcXFxhcHBzXFxcXG1vYmlsZVwiO2NvbnN0IF9fdml0ZV9pbmplY3RlZF9vcmlnaW5hbF9maWxlbmFtZSA9IFwiQzpcXFxcVXNlcnNcXFxcYXJ0ZGlcXFxcRG9jdW1lbnRzXFxcXERFVlxcXFxscmctYmxcXFxcYXBwc1xcXFxtb2JpbGVcXFxcdml0ZS5jb25maWcuanNcIjtjb25zdCBfX3ZpdGVfaW5qZWN0ZWRfb3JpZ2luYWxfaW1wb3J0X21ldGFfdXJsID0gXCJmaWxlOi8vL0M6L1VzZXJzL2FydGRpL0RvY3VtZW50cy9ERVYvbHJnLWJsL2FwcHMvbW9iaWxlL3ZpdGUuY29uZmlnLmpzXCI7aW1wb3J0IHRhaWx3aW5kY3NzIGZyb20gXCJAdGFpbHdpbmRjc3Mvdml0ZVwiO1xuaW1wb3J0IGxlZ2FjeSBmcm9tICdAdml0ZWpzL3BsdWdpbi1sZWdhY3knO1xuaW1wb3J0IHJlYWN0IGZyb20gJ0B2aXRlanMvcGx1Z2luLXJlYWN0JztcbmltcG9ydCB7IGRlZmluZUNvbmZpZyB9IGZyb20gJ3ZpdGUnO1xuLy8gaHR0cHM6Ly92aXRlLmRldi9jb25maWcvXG5leHBvcnQgZGVmYXVsdCBkZWZpbmVDb25maWcoe1xuICAgIHBsdWdpbnM6IFtcbiAgICAgICAgcmVhY3QoKSxcbiAgICAgICAgdGFpbHdpbmRjc3MoKSxcbiAgICAgICAgbGVnYWN5KClcbiAgICBdLFxuICAgIHNlcnZlcjoge1xuICAgICAgICBwb3J0OiA4MDAxLFxuICAgICAgICBwcm94eToge1xuICAgICAgICAgICAgJy9hcGknOiB7XG4gICAgICAgICAgICAgICAgdGFyZ2V0OiAnaHR0cDovL2xvY2FsaG9zdDozMDAwJyxcbiAgICAgICAgICAgICAgICBjaGFuZ2VPcmlnaW46IHRydWUsXG4gICAgICAgICAgICAgICAgcmV3cml0ZTogZnVuY3Rpb24gKHBhdGgpIHsgcmV0dXJuIHBhdGgucmVwbGFjZSgvXlxcL2FwaS8sICcnKTsgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxufSk7XG4iXSwKICAibWFwcGluZ3MiOiAiO0FBQStVLE9BQU8saUJBQWlCO0FBQ3ZXLE9BQU8sWUFBWTtBQUNuQixPQUFPLFdBQVc7QUFDbEIsU0FBUyxvQkFBb0I7QUFFN0IsSUFBTyxzQkFBUSxhQUFhO0FBQUEsRUFDeEIsU0FBUztBQUFBLElBQ0wsTUFBTTtBQUFBLElBQ04sWUFBWTtBQUFBLElBQ1osT0FBTztBQUFBLEVBQ1g7QUFBQSxFQUNBLFFBQVE7QUFBQSxJQUNKLE1BQU07QUFBQSxJQUNOLE9BQU87QUFBQSxNQUNILFFBQVE7QUFBQSxRQUNKLFFBQVE7QUFBQSxRQUNSLGNBQWM7QUFBQSxRQUNkLFNBQVMsU0FBVSxNQUFNO0FBQUUsaUJBQU8sS0FBSyxRQUFRLFVBQVUsRUFBRTtBQUFBLFFBQUc7QUFBQSxNQUNsRTtBQUFBLElBQ0o7QUFBQSxFQUNKO0FBQ0osQ0FBQzsiLAogICJuYW1lcyI6IFtdCn0K
