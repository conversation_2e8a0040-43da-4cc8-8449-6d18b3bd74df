import type { INotificationEntity } from '../../../interfaces/entity/i-notification-entity.ts';
import { apiClient, type ApiClient } from '@/lib/api/client/api-client.ts';

export class NotificationManagerApiService {
  private readonly apiClient: ApiClient;

  constructor(apiClient: ApiClient) {
    this.apiClient = apiClient;
  }

  async getNotificationsByUserId(userId: string): Promise<INotificationEntity[]> {
    const response = await this.apiClient.get(`/api/manager/notifications/user/${userId}`);
    return response.data;
  }

  async markAsRead(id: string): Promise<INotificationEntity> {
    const response = await this.apiClient.patch(`/api/manager/notifications/${id}/mark-read`);
    return response.data;
  }
}

export const notificationManagerApiService = new NotificationManagerApiService(apiClient);
