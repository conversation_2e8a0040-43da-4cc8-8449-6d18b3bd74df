'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import type { DataTableConfig } from '@/interfaces/datatable';
import type { IStopEntity } from '@/interfaces/entity/i-stop-entity';
import type { IPaginationParams } from '@/interfaces/pagination';
import { tourManagerApiService } from '@/lib/api/service/tour-manager-api-service';
import { ColumnDef } from '@tanstack/react-table';
import {
  CalendarIcon,
  CheckCircleIcon,
  Clock,
  EyeIcon,
  MapPinIcon,
  TruckIcon,
  UserIcon,
  XCircleIcon,
} from 'lucide-react';
import { PaginatedDataTable } from '../../../layout/paginated-datatable';

interface StopsTableProps {
  tourId: string;
  className?: string;
}

export function StopsTable({ tourId, className }: StopsTableProps) {
  const handleViewStop = (stop: IStopEntity) => {
    console.log('View stop:', stop);
    // TODO: Navigate to stop detail view
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  const formatDateTime = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return (
      dateObj.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
      }) +
      ' à ' +
      dateObj.toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit',
      })
    );
  };

  const getDeliveryStatusLabel = (completion: IStopEntity['completion']) => {
    if (!completion || !completion.deliveryStatus) {
      return 'En attente';
    }
    switch (completion.deliveryStatus) {
      case 'COMPLETED':
        return 'Terminé';
      case 'FAILED':
        return 'Annulé';
      case 'PENDING':
        return 'En cours';
      default:
        return completion.deliveryStatus;
    }
  };

  const getDeliveryStatusColor = (completion: IStopEntity['completion']) => {
    if (!completion || !completion.deliveryStatus) {
      return 'outline';
    }
    switch (completion.deliveryStatus) {
      case 'COMPLETED':
        return 'default';
      case 'FAILED':
        return 'destructive';
      case 'PENDING':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getStatusIcon = (completion: IStopEntity['completion']) => {
    if (!completion || !completion.deliveryStatus) {
      return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
    switch (completion.deliveryStatus) {
      case 'COMPLETED':
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case 'FAILED':
        return <XCircleIcon className="h-4 w-4 text-red-600" />;
      case 'PENDING':
        return <TruckIcon className="h-4 w-4 text-blue-600" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const columns: ColumnDef<IStopEntity>[] = [
    {
      header: 'N° Ordre',
      accessorKey: 'sequenceInTour',
      cell: ({ row }) => (
        <div className="flex items-center justify-center">
          <div className="bg-primary/10 text-primary font-medium rounded-full w-8 h-8 flex items-center justify-center text-sm">
            {row.original.sequenceInTour}
          </div>
        </div>
      ),
      size: 80,
      enableHiding: false,
    },
    {
      header: 'Client',
      accessorKey: 'client.name',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <div className="bg-blue-50 p-1.5 rounded">
            <UserIcon className="h-4 w-4 text-blue-600" />
          </div>
          <div>
            <div className="font-medium max-w-48 truncate">
              {row.original.client?.name || row.original.originalClientInfo?.name || 'N/A'}
            </div>
            {row.original.client?.code && (
              <div className="text-sm text-muted-foreground">{row.original.client.code}</div>
            )}
          </div>
        </div>
      ),
      size: 200,
    },
    {
      header: 'Adresse',
      accessorKey: 'originalClientInfo.address.line1',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <MapPinIcon className="h-4 w-4 text-muted-foreground" />
          <div className="max-w-48 truncate">
            {row.original.originalClientInfo?.address?.line1 || 'N/A'}
          </div>
        </div>
      ),
      size: 200,
      enableSorting: false,
    },
    {
      header: 'Horaire prévu',
      accessorKey: 'deliveryTimeWindow',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{row.original.deliveryTimeWindow || 'Non défini'}</span>
        </div>
      ),
      size: 120,
      enableSorting: false,
    },
    {
      header: 'Statut',
      accessorKey: 'completion.deliveryStatus',
      cell: ({ row }) => {
        const completion = row.original.completion;
        return (
          <div className="flex items-center gap-2">
            {getStatusIcon(completion)}
            <Badge variant={getDeliveryStatusColor(completion)} className="text-xs">
              {getDeliveryStatusLabel(completion)}
            </Badge>
          </div>
        );
      },
      size: 120,
      enableSorting: true,
    },
    {
      header: 'Heure réelle',
      accessorKey: 'completion.completedAt',
      cell: ({ row }) => {
        const completedAt = row.original.completion?.completedAt;
        return (
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">
              {completedAt ? formatDateTime(completedAt) : 'Non livré'}
            </span>
          </div>
        );
      },
      size: 150,
      enableSorting: true,
    },
    {
      header: 'Actions',
      id: 'actions',
      cell: ({ row }) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewStop(row.original)}
          className="h-8 px-2"
        >
          <EyeIcon className="h-4 w-4" />
          <span className="sr-only">Voir le détail</span>
        </Button>
      ),
      size: 80,
      enableSorting: false,
      enableHiding: false,
    },
  ];

  const config: DataTableConfig<IStopEntity> = {
    export: {
      enabled: true,
      fileName: 'arrets-tournee',
      sheetName: 'Arrêts',
      excludeColumns: ['actions'],
      columnMap: {
        sequenceInTour: 'N° Ordre',
        'client.name': 'Client',
        'originalClientInfo.address.line1': 'Adresse',
        deliveryTimeWindow: 'Horaire prévu',
        'completion.deliveryStatus': 'Statut',
        'completion.completedAt': 'Heure réelle',
      },
      valueTransformers: {
        'client.name': (value, row) => {
          const stop = row as IStopEntity;
          return stop.client?.name || stop.originalClientInfo?.name || 'N/A';
        },
        'originalClientInfo.address.line1': (value, row) => {
          const stop = row as IStopEntity;
          return stop.originalClientInfo?.address?.line1 || 'N/A';
        },
        'completion.deliveryStatus': (value, row) => {
          const stop = row as IStopEntity;
          return getDeliveryStatusLabel(stop.completion);
        },
        'completion.completedAt': (value, row) => {
          const stop = row as IStopEntity;
          return stop.completion?.completedAt
            ? formatDateTime(stop.completion.completedAt)
            : 'Non livré';
        },
      },
    },
    columns,
    filters: [
      {
        columnKey: 'completion.deliveryStatus',
        label: 'Statut de livraison',
        type: 'select',
        options: [
          { label: 'Tous les statuts', value: 'all' },
          { label: 'En cours', value: 'PENDING' },
          { label: 'Terminé', value: 'COMPLETED' },
          { label: 'Annulé', value: 'FAILED' },
        ],
        placeholder: 'Filtrer par statut',
      },
    ],
    enableSelection: false,
    enableColumnVisibility: true,
    defaultPageSize: 20,
    pageSizeOptions: [10, 20, 50],
  };

  const customQueryFn = (params: IPaginationParams) => {
    return tourManagerApiService.getTourStops(tourId, params);
  };

  return (
    <div className={className}>
      <PaginatedDataTable
        queryKey={['tour-stops', tourId]}
        queryFn={customQueryFn}
        config={config}
      />
    </div>
  );
}
