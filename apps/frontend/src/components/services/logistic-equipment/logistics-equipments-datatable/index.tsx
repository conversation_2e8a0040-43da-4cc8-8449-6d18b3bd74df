import { ColumnDef } from '@tanstack/react-table';
import type { IStopEntity } from '@/interfaces/entity/i-stop-entity.ts';
import { Checkbox } from '@/components/ui/checkbox.tsx';
import { PaginatedDataTable } from '@/components/layout/paginated-datatable';
import { stopManagerApiService } from '@/lib/api/service/stop-manager-api-service.ts';
import type { DataTableConfig } from '@/interfaces/datatable.ts';
import type { IPaginationParams } from '@/interfaces/pagination.ts';
import { DeliveryStatus } from '@/interfaces/enum/stop-completion.enum';
import { Badge } from '@/components/ui/badge.tsx';
import { CalendarIcon } from 'lucide-react';
import { useState } from 'react';
import { Button } from '@/components/ui/button.tsx';
import { Calendar } from '@/components/ui/calendar.tsx';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover.tsx';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils.ts';
import { EditIcon, PlusIcon, TrashIcon } from 'lucide-react';

interface LogisticsEquipmentsSearchParams extends Record<string, unknown> {
  search?: string;
  deliveryDate?: string;
  hasEquipmentReturns?: string;
}

interface LogisticsEquipmentsDataTableProps {
  className?: string;
}

const columns: ColumnDef<IStopEntity>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() || (table.getIsSomePageRowsSelected() && 'indeterminate')
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Tout sélectionner"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Sélectionner la ligne"
      />
    ),
    size: 28,
    enableSorting: false,
    enableHiding: false,
  },
  {
    header: 'Date de livraison',
    accessorKey: 'tour.deliveryDate',
    cell: ({ row }) => {
      const deliveryDate = row.original.tour.deliveryDate;
      const completedAt = row.original.completion?.completedAt;
      return (
        <div className="flex flex-col gap-1">
          <span className="max-w-48 truncate">
            {new Date(deliveryDate).toLocaleDateString('fr-FR')}
          </span>
          {completedAt && (
            <span className="text-xs text-muted-foreground">
              Livré le {new Date(completedAt).toLocaleDateString('fr-FR')} à{' '}
              {new Date(completedAt).toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit',
              })}
            </span>
          )}
        </div>
      );
    },
    size: 150,
    enableHiding: false,
    enableSorting: true,
  },
  {
    header: 'N° Tournée',
    accessorKey: 'tour.tourIdentifier.originalNumber',
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="max-w-48 truncate">{row.original.tour.tourIdentifier.originalNumber}</span>
      </div>
    ),
    size: 100,
    enableSorting: true,
  },
  {
    header: 'Client',
    accessorKey: 'originalClientInfo.name',
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="max-w-48 truncate">
          {row.original.originalClientInfo.name || row.original.client?.name || 'N/A'}
        </span>
      </div>
    ),
    size: 120,
    enableSorting: true,
  },
  {
    header: 'Chauffeur',
    accessorKey: 'driver', // TODO: Add driver relationship
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="max-w-48 truncate text-muted-foreground">TBD</span>
      </div>
    ),
    size: 100,
    enableSorting: false,
  },
  {
    header: 'Réceptionniste',
    accessorKey: 'recipient', // TODO: Add recipient info
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="max-w-48 truncate text-muted-foreground">TBD</span>
      </div>
    ),
    size: 100,
    enableSorting: false,
  },
  {
    header: 'Total Chauffeur',
    accessorKey: 'completion.totalEquipment',
    cell: ({ row }) => {
      const completion = row.original.completion;
      const returned = completion?.returnedEquipment;
      const total =
        (returned?.palletCount || 0) + (returned?.rollCount || 0) + (returned?.packageCount || 0);
      return (
        <div className="flex items-center gap-2">
          <span className="max-w-48 truncate">{total > 0 ? total : '-'}</span>
        </div>
      );
    },
    size: 90,
    enableSorting: true,
  },
  {
    header: 'Total Réceptionniste',
    accessorKey: 'recipient.totalEquipment', // TODO: Add recipient equipment count
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="max-w-48 truncate text-muted-foreground">TBD</span>
      </div>
    ),
    size: 90,
    enableSorting: false,
  },
  {
    header: 'Écart',
    accessorKey: 'equipmentDifference', // TODO: Calculate difference when recipient data available
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <span className="max-w-48 truncate text-muted-foreground">TBD</span>
      </div>
    ),
    size: 50,
    enableSorting: false,
  },
  {
    header: 'Statut',
    accessorKey: 'completion.deliveryStatus',
    cell: ({ row }) => {
      const status = row.original.completion?.deliveryStatus;
      const returned = row.original.completion?.returnedEquipment;
      const hasEquipment =
        (returned?.palletCount || 0) + (returned?.rollCount || 0) + (returned?.packageCount || 0) >
        0;

      let badgeVariant: 'default' | 'secondary' | 'destructive' | 'outline' = 'outline';
      let statusText = 'PENDING';

      if (status === DeliveryStatus.COMPLETED) {
        badgeVariant = hasEquipment ? 'default' : 'secondary';
        statusText = hasEquipment ? 'OK' : 'NO EQUIPMENT';
      } else if (status === DeliveryStatus.FAILED) {
        badgeVariant = 'destructive';
        statusText = 'NOK';
      }

      return (
        <Badge variant={badgeVariant} className="text-xs">
          {statusText}
        </Badge>
      );
    },
    size: 80,
    enableSorting: true,
  },
];

export function LogisticsEquipmentsDataTable({ className }: LogisticsEquipmentsDataTableProps) {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  const handleEditLogisticEquipment = (selectedLogisticsEquipments: IStopEntity[]) => {
    console.log('Edit equipment returns:', selectedLogisticsEquipments);
  };

  const handleDeleteLogisticEquipment = (selectedLogisticsEquipments: IStopEntity[]) => {
    console.log('Delete equipment returns:', selectedLogisticsEquipments);
  };

  const handleAddLogisticEquipment = () => {
    // Not applicable for equipment returns
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      setIsDatePickerOpen(false);
    }
  };

  const config: DataTableConfig<IStopEntity, LogisticsEquipmentsSearchParams> = {
    export: {
      enabled: true,
      fileName: 'retours-equipements',
      sheetName: "Retours d'équipements",
      excludeColumns: ['select', 'actions'],
      columnMap: {
        'tour.deliveryDate': 'Date de livraison',
        'tour.tourIdentifier.originalNumber': 'N° Tournée',
        'originalClientInfo.name': 'Client',
        driver: 'Chauffeur',
        recipient: 'Réceptionniste',
        'completion.totalEquipment': 'Total Chauffeur',
        'recipient.totalEquipment': 'Total Réceptionniste',
        equipmentDifference: 'Écart',
        'completion.deliveryStatus': 'Statut',
      },
      valueTransformers: {
        'tour.deliveryDate': (value, row) => {
          const deliveryDate = (row as IStopEntity).tour.deliveryDate;
          return new Date(deliveryDate).toLocaleDateString('fr-FR');
        },
        'tour.tourIdentifier.originalNumber': (value, row) => {
          return (row as IStopEntity).tour.tourIdentifier.originalNumber;
        },
        'originalClientInfo.name': (value, row) => {
          const stop = row as IStopEntity;
          return stop.originalClientInfo.name || stop.client?.name || 'N/A';
        },
        driver: () => 'TBD',
        recipient: () => 'TBD',
        'completion.totalEquipment': (value, row) => {
          const completion = (row as IStopEntity).completion;
          const returned = completion?.returnedEquipment;
          const total =
            (returned?.palletCount || 0) +
            (returned?.rollCount || 0) +
            (returned?.packageCount || 0);
          return total > 0 ? total : '-';
        },
        'recipient.totalEquipment': () => 'TBD',
        equipmentDifference: () => 'TBD',
        'completion.deliveryStatus': (value, row) => {
          const status = (row as IStopEntity).completion?.deliveryStatus;
          const returned = (row as IStopEntity).completion?.returnedEquipment;
          const hasEquipment =
            (returned?.palletCount || 0) +
              (returned?.rollCount || 0) +
              (returned?.packageCount || 0) >
            0;

          if (status === DeliveryStatus.COMPLETED) {
            return hasEquipment ? 'OK' : 'NO EQUIPMENT';
          } else if (status === DeliveryStatus.FAILED) {
            return 'NOK';
          }
          return 'PENDING';
        },
      },
    },
    columns,
    search: {
      enabled: true,
      placeholder: 'Rechercher par client, tournée...',
      searchKey: 'search',
    },
    enableSelection: true,
    enableColumnVisibility: true,
    defaultPageSize: 10,
    pageSizeOptions: [5, 10, 25, 50, 100],
    actions: [],
  };

  const customQueryFn = (params: IPaginationParams & LogisticsEquipmentsSearchParams) => {
    const dateStr = selectedDate.toISOString().split('T')[0];
    return stopManagerApiService.getStopsByDeliveryDate(dateStr, {
      ...params,
      hasEquipmentReturns: 'true',
    } as IPaginationParams);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Popover open={isDatePickerOpen} onOpenChange={setIsDatePickerOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                'w-[280px] justify-start text-left font-normal',
                !selectedDate && 'text-muted-foreground',
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {selectedDate ? (
                format(selectedDate, 'PPP', { locale: fr })
              ) : (
                <span>Sélectionner une date</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={handleDateChange}
              initialFocus
              locale={fr}
            />
          </PopoverContent>
        </Popover>
      </div>

      <PaginatedDataTable
        queryKey={['equipment-returns', 'paginated', selectedDate.toISOString().split('T')[0]]}
        queryFn={customQueryFn}
        config={config}
        className={className}
      />
    </div>
  );
}
