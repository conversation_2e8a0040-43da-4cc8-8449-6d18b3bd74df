import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notificationManagerApiService } from '@/lib/api/service/notification-manager-api-service.ts';
import { keycloakService } from '@/lib/services/keycloak.service.ts';
import type { INotificationEntity } from '@/interfaces/entity/i-notification-entity.ts';

export const notificationQueryKeys = {
  all: ['notifications'] as const,
  byUserId: (userId: string) => [...notificationQueryKeys.all, 'user', userId] as const,
};

export const useNotificationsByUserIdQuery = (userId: string) => {
  return useQuery({
    queryKey: notificationQueryKeys.byUserId(userId),
    queryFn: async (): Promise<INotificationEntity[]> => {
      if (!keycloakService.keycloak.authenticated) {
        return [];
      }
      return await notificationManagerApiService.getNotificationsByUserId(userId);
    },
    enabled: keycloakService.keycloak.authenticated && !!userId,
  });
};

export const useMarkNotificationAsReadMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => notificationManagerApiService.markAsRead(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: notificationQueryKeys.all });
    },
  });
};
