import 'dotenv/config';
import 'reflect-metadata';

import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';

export const appDatasource = new DataSource({
  type: 'postgres',
  host: process.env.DB_CONNECTION_ADDRESS,
  port: parseInt(process.env.DB_CONNECTION_PORT as string, 10),
  username: process.env.DB_CONNECTION_LOGIN,
  password: process.env.DB_CONNECTION_PASSWORD,
  database: process.env.DB_CONNECTION_DATABASE,
  synchronize: true,
  entities: [`${__dirname}/domain/entity/**/*.entity.{ts,js}`],
  migrations: [`${__dirname}/migrations/**`],
  namingStrategy: new SnakeNamingStrategy(),
});
